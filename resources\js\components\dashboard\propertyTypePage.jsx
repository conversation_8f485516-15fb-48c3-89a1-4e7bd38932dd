import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PropertyTypesSearch from './PropertyTypesSearch';
import PropertyTypeTable from './PropertyTypeTable';
import {
  Plus,
  Settings
} from 'lucide-react';
import propertyTypeAPI from '../../services/propertyTypeAPI';
import PropertyTypeModal from './PropertyTypeModal';
import { showAlert } from '../../utils/sweetAlert';

export default function PropertyTypePage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedPropertyType, setSelectedPropertyType] = useState(null);
  const [pagination, setPagination] = useState({
    current_page: 1,
    per_page: 15,
    total: 0,
    last_page: 1
  });

  // Search and filter effect
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      fetchPropertyTypes(1);
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, statusFilter]);

  // Handle create
  const handleCreate = () => {
    setSelectedPropertyType(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  // Handle edit
  const handleEdit = (propertyType) => {
    setSelectedPropertyType(propertyType);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  // Handle delete
  const handleDelete = async (propertyType) => {
    if (window.confirm(`Are you sure you want to delete "${propertyType.name}"?`)) {
      try {
        await propertyTypeAPI.delete(propertyType.id);
        showAlert('success', 'Success', 'Property type deleted successfully');
        fetchPropertyTypes();
      } catch (error) {
        showAlert('error', 'Error', error.message || 'Failed to delete property type');
      }
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (propertyType) => {
    try {
      await propertyTypeAPI.toggleStatus(propertyType.id);
      showAlert('success', 'Success', 'Property type status updated successfully');
      fetchPropertyTypes();
    } catch (error) {
      showAlert('error', 'Error', error.message || 'Failed to update property type status');
    }
  };

  // Handle modal success
  const handleModalSuccess = () => {
    setIsModalOpen(false);
    fetchPropertyTypes();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Property Types</h1>
          <p className="text-muted-foreground">
            Manage property type categories and classifications
          </p>
        </div>
        <Button onClick={handleCreate} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Property Type
        </Button>
      </div>

      {/* Filters */}
      <PropertyTypesSearch
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        handleSearch={handleSearch}
        setSearchTerm={setSearchTerm}
        setStatusFilter={setStatusFilter}
      />

      {/* Property Types Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-20"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : propertyTypes.length > 0 ? (
        <PropertyTypeTable
          propertyTypes={propertyTypes}
        />
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No property types found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all'
                ? 'No property types match your current filters.'
                : 'Get started by creating your first property type.'
              }
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button onClick={handleCreate} className="gap-2">
                <Plus className="h-4 w-4" />
                Add Property Type
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {pagination.last_page > 1 && (
        <div className="flex justify-center">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchPropertyTypes(pagination.current_page - 1)}
              disabled={pagination.current_page === 1}
            >
              Previous
            </Button>

            <span className="text-sm text-gray-600">
              Page {pagination.current_page} of {pagination.last_page}
            </span>

            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchPropertyTypes(pagination.current_page + 1)}
              disabled={pagination.current_page === pagination.last_page}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Modal */}
      <PropertyTypeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleModalSuccess}
        propertyType={selectedPropertyType}
        mode={modalMode}
      />
    </div>
  );
}
