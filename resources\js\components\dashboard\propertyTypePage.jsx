
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Search, 
  Plus, 
  Edit2, 
  Trash2, 
  Power, 
  Filter,
  MoreHorizontal,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import propertyTypeAPI from '../../services/propertyTypeAPI';
import PropertyTypeModal from './PropertyTypeModal';
import { showAlert } from '../../utils/alertUtils';

export default function PropertyTypePage() {
  const [propertyTypes, setPropertyTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedPropertyType, setSelectedPropertyType] = useState(null);
  const [pagination, setPagination] = useState({
    current_page: 1,
    per_page: 15,
    total: 0,
    last_page: 1
  });

  // Fetch property types
  const fetchPropertyTypes = async (page = 1) => {
    try {
      setLoading(true);
      const params = {
        page,
        per_page: pagination.per_page,
        search: searchTerm,
        is_active: statusFilter === 'all' ? undefined : statusFilter === 'active'
      };

      const response = await propertyTypeAPI.getAll(params);
      
      if (response.success) {
        setPropertyTypes(response.data.data);
        setPagination({
          current_page: response.data.current_page,
          per_page: response.data.per_page,
          total: response.data.total,
          last_page: response.data.last_page
        });
      }
    } catch (error) {
      console.error('Error fetching property types:', error);
      showAlert('error', 'Error', 'Failed to fetch property types');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchPropertyTypes();
  }, []);

  // Search and filter effect
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      fetchPropertyTypes(1);
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, statusFilter]);

  // Handle create
  const handleCreate = () => {
    setSelectedPropertyType(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  // Handle edit
  const handleEdit = (propertyType) => {
    setSelectedPropertyType(propertyType);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  // Handle view
  const handleView = (propertyType) => {
    setSelectedPropertyType(propertyType);
    setModalMode('view');
    setIsModalOpen(true);
  };

  // Handle delete
  const handleDelete = async (propertyType) => {
    if (window.confirm(`Are you sure you want to delete "${propertyType.name}"?`)) {
      try {
        await propertyTypeAPI.delete(propertyType.id);
        showAlert('success', 'Success', 'Property type deleted successfully');
        fetchPropertyTypes();
      } catch (error) {
        showAlert('error', 'Error', error.message || 'Failed to delete property type');
      }
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (propertyType) => {
    try {
      await propertyTypeAPI.toggleStatus(propertyType.id);
      showAlert('success', 'Success', 'Property type status updated successfully');
      fetchPropertyTypes();
    } catch (error) {
      showAlert('error', 'Error', error.message || 'Failed to update property type status');
    }
  };

  // Handle modal success
  const handleModalSuccess = () => {
    setIsModalOpen(false);
    fetchPropertyTypes();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Property Types</h1>
          <p className="text-muted-foreground">
            Manage property type categories and classifications
          </p>
        </div>
        <Button onClick={handleCreate} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Property Type
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search property types..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Property Types Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-20"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : propertyTypes.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {propertyTypes.map((propertyType) => (
            <Card key={propertyType.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg text-gray-900 mb-2">
                      {propertyType.name}
                    </h3>
                    {propertyType.description && (
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {propertyType.description}
                      </p>
                    )}
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleView(propertyType)}>
                        <Settings className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(propertyType)}>
                        <Edit2 className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleToggleStatus(propertyType)}>
                        <Power className="mr-2 h-4 w-4" />
                        {propertyType.is_active ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDelete(propertyType)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="flex justify-between items-center">
                  <Badge 
                    variant={propertyType.is_active ? "default" : "secondary"}
                    className={propertyType.is_active ? "bg-green-500" : "bg-gray-500"}
                  >
                    {propertyType.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                  <div className="text-sm text-gray-500">
                    Order: {propertyType.sort_order}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No property types found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all' 
                ? 'No property types match your current filters.' 
                : 'Get started by creating your first property type.'
              }
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button onClick={handleCreate} className="gap-2">
                <Plus className="h-4 w-4" />
                Add Property Type
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {pagination.last_page > 1 && (
        <div className="flex justify-center">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchPropertyTypes(pagination.current_page - 1)}
              disabled={pagination.current_page === 1}
            >
              Previous
            </Button>
            
            <span className="text-sm text-gray-600">
              Page {pagination.current_page} of {pagination.last_page}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchPropertyTypes(pagination.current_page + 1)}
              disabled={pagination.current_page === pagination.last_page}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Modal */}
      <PropertyTypeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleModalSuccess}
        propertyType={selectedPropertyType}
        mode={modalMode}
      />
    </div>
  );
}
