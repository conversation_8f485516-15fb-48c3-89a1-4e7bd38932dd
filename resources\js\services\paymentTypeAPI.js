import axios from "axios";

const API_URL = '/api/payment-types';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add JWT token to request headers if available
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Payment Type API functions
const paymentTypeAPI = {
    // Get all payment types with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            // Handle paginated response
            if (response.data.success && response.data.data && response.data.data.data) {
                return response.data; // Return full response for pagination info
            }
            return response.data;
        } catch (error) {
            console.error('Error fetching payment types:', error);
            throw error.response?.data || error;
        }
    },

    // Get payment type by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching payment type:', error);
            throw error.response?.data || error;
        }
    },

    // Create new payment type
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error creating payment type:', error);
            throw error.response?.data || error;
        }
    },

    // Update payment type
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error updating payment type:', error);
            throw error.response?.data || error;
        }
    },

    // Delete payment type
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error deleting payment type:', error);
            throw error.response?.data || error;
        }
    },

    // Toggle payment type status (active/inactive)
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error toggling payment type status:', error);
            throw error.response?.data || error;
        }
    },

    // Get dropdown options (active payment types only)
    getDropdown: async () => {
        try {
            const response = await api.get(`${API_URL}/dropdown`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching payment types dropdown:', error);
            throw error.response?.data || error;
        }
    }
}

export default paymentTypeAPI;
