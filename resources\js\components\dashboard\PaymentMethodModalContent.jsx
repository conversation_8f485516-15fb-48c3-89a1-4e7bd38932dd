import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Save, FileText, Package } from "lucide-react";

const PaymentMethodModalContent = ({
  isOpen,
  onClose,
  method,
  mode,
  onSave,
}) => {
    const [formData, setFormData] = React.useState(method || { name: "", description: "", is_active: true });
    const [saving, setSaving] = React.useState(false);
  
    const handleSave = async () => {
      if (!formData.name.trim()) {
        toast.error("Name is required");
        return;
      }
      setSaving(true);
      try {
        await onSave(formData);
        setSaving(false);
        onClose();
      } catch (error) {
        setSaving(false);
        toast.error("Failed to save payment method");
      }
    };
  
    if (!isOpen) return null;
    return(
         <div className="flex-1 flex flex-col overflow-y-auto px-6 py-4 min-h-0">
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <label className="text-right text-sm font-medium col-span-1">Name</label>
                  <Input
                    value={formData.name}
                    disabled={mode === 'view'}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label className="text-right text-sm font-medium col-span-1">Description</label>
                  <textarea
                    className="col-span-3 w-full border rounded p-2"
                    rows={3}
                    value={formData.description}
                    disabled={mode === 'view'}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  />
                </div>
                {mode !== 'view' && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label className="text-right text-sm font-medium col-span-1 flex items-center">Active</label>
                    <div className="col-span-3 flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                      />
                      <span>Active</span>
                    </div>
                  </div>
                )}
              </div>
              {mode !== 'view' && (
                <div className="flex justify-end gap-2 mt-4">
                  <Button variant="outline" onClick={onClose} disabled={saving}>Cancel</Button>
                  <Button onClick={handleSave} disabled={saving}>
                    {saving ? 'Saving...' : mode === 'edit' ? 'Update' : 'Create'}
                  </Button>
                </div>
              )}
            </div>
    )};
    export default PaymentMethodModalContent;