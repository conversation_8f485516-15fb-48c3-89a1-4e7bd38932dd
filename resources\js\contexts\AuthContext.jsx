import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../services/authAPI';
import { moduleAvailabilityService } from '../services/moduleAvailabilityService';

const AuthContext = createContext({
  user: null,
  role: null,
  permissions: {},
  accessibleModules: [],
  availableModules: [],
  moduleDetails: {},
  hasModuleAccess: () => false,
  hasPermission: () => false,
  isModuleAvailable: () => false,
  canAccessModule: () => false,
  login: () => {},
  logout: () => {},
  loading: true,  
  isAuthenticated: false
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [role, setRole] = useState(null);
  const [permissions, setPermissions] = useState({});
  const [accessibleModules, setAccessibleModules] = useState([]);
  const [availableModules, setAvailableModules] = useState([]);
  const [moduleDetails, setModuleDetails] = useState({});
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const loadModuleAvailability = async () => {
    try {
      const response = await moduleAvailabilityService.getAvailableModules();
      if (response.success) {
        setAvailableModules(response.data.available_keys);
        setModuleDetails(response.data.modules);
      } else {
        setFallbackModules();
      }
    } catch (error) { 
      setFallbackModules();
    }
  };

  const setFallbackModules = () => {
    const fallbackModules = [
      'dashboard',
      'land-acquisition',
      'role',
      'settings',
      'landowners',
      'customer',
      'property-status',
      'unit-type',
      'property-stage',
      'tenants',
      'property-service',
      'rent-type',
      'lease-type',
      'invoice',
      'payment-type',
      'payment-status',
      'payment-method'
       // <-- ensure customer module is included here
    ];

    setAvailableModules(fallbackModules);
    setModuleDetails({
      'dashboard': { available: true, name: 'Dashboard', key: 'dashboard' },
      'land-acquisition': { available: true, name: 'Land Acquisition', key: 'land-acquisition' },
      'role': { available: true, name: 'Role Management', key: 'role' },
      'settings': { available: true, name: 'Settings', key: 'settings' },
      'landowners': { available: true, name: 'Land Owners', key: 'landowners' },
      'customer': { available: true, name: 'Customer', key: 'customer' },
      'property-status': { available: true, name: 'Property Status', key: 'property-status' },
      'unit-type': { available: true, name: 'Unit Type', key: 'unit-type' },
      'property-stage': { available: true, name: 'Property Stage', key: 'property-stage' },
      'tenants': { available: true, name: 'Tenants', key: 'tenants' },
      'property-service': { available: true, name: 'Property Service', key: 'property-service' },
      'rent-type': { available: true, name: 'Rent Type', key: 'rent-type' },
      'lease-type': { available: true, name: 'Lease Type', key: 'lease-type' },
      'invoice': { available: true, name: 'Invoice', key: 'invoice' },
      'payment-type': { available: true, name: 'Payment Type', key: 'payment-type' },
      'payment-status': { available: true, name: 'Payment Status', key: 'payment-status' },
      'payment-method': { available: true, name: 'Payment Method', key: 'payment-method' }// add this
    });
  };

  useEffect(() => {
    const loadUserData = async () => {
      await loadModuleAvailability();

      if (authAPI.isAuthenticated()) {
        try {
          const response = await authAPI.getPermissions();
          if (response.success) {
            setUser(response.data.user);
            setRole(response.data.role);
            setPermissions(response.data.permissions);
            setAccessibleModules(response.data.accessible_modules);
            setIsAuthenticated(true);
          } else {
            authAPI.removeToken();
            setIsAuthenticated(false);
          }
        } catch (error) {
          authAPI.removeToken();
          setIsAuthenticated(false);
        }
      } else {
        setIsAuthenticated(false);
      }
      setLoading(false);
    };

    loadUserData();
  }, []);

  const hasModuleAccess = (moduleKey) => {
    return accessibleModules.includes(moduleKey);
  };

  const isModuleAvailable = (moduleKey) => {
    return availableModules.includes(moduleKey);
  };

  const canAccessModule = (moduleKey) => {
    const hasPermission = hasModuleAccess(moduleKey);

    const coreModules = [
      'dashboard', 'settings', 'role', 'land-acquisition', 'landowners',
      'analytics', 'project', 'lifecycle', 'employees', 'contractors',
      'assign-contractor', 'assign-vendor', 'assign-employee', 'vendor-type',
      'vendor', 'property-amenity', 'property-type', 'property-status','customer', 'orders', 'components',
      'reports', 'word-assistant', 'country', 'language', 'currency','unit-type', 'property-stage', 'tenants', 'property-service', 
      'rent-type', 'lease-type', 'invoice', 'payment-type','payment-status','payment-method'
      
    ];

    // For core modules, check if module is available or if user has permission
    if (coreModules.includes(moduleKey)) {
      return isModuleAvailable(moduleKey) || hasPermission;
    }
    
    if (availableModules.length === 0) return hasPermission;

    return isModuleAvailable(moduleKey) && hasPermission;
  };

  const hasPermission = (moduleKey, permission) => {
    const modulePermissions = permissions[moduleKey] || [];
    return modulePermissions.includes(permission);
  };

  const login = async (credentials) => {
    try {
      const response = await authAPI.login(credentials);
      if (response.success) {
        setUser(response.data.user);
        setRole(response.data.user.role);
        setPermissions(response.data.user.role?.module_permissions || {});
        setAccessibleModules(response.data.user.role?.accessible_modules || []);
        setIsAuthenticated(true);
        await loadModuleAvailability();
        return response;
      }
      return response;
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (_) {}
    setUser(null);
    setRole(null);
    setPermissions({});
    setAccessibleModules([]);
    setIsAuthenticated(false);
  };

  const register = async (userData) => {
    try {
      const response = await authAPI.register(userData);
      if (response.success) {
        setUser(response.data.user);
        setRole(response.data.user.role);
        setPermissions(response.data.user.role?.module_permissions || {});
        setAccessibleModules(response.data.user.role?.accessible_modules || []);
        setIsAuthenticated(true);
        await loadModuleAvailability();
        return response;
      }
      return response;
    } catch (error) {
      throw error;
    }
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await authAPI.updateProfile(profileData);
      if (response.success) {
        setUser(response.data);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };

  const refreshUserData = async () => {
    try {
      if (authAPI.isAuthenticated()) {
        const response = await authAPI.profile();
        if (response.success) {
          setUser(response.data);
          setRole(response.data.role);
          setPermissions(response.data.role?.module_permissions || {});
          setAccessibleModules(response.data.role?.accessible_modules || []);
          await loadModuleAvailability();
       
          return response;
        }
      }
    } catch (error) {
   
      throw error;
    }
  };

  const value = {
    user,
    role,
    permissions,
    accessibleModules,
    availableModules,
    moduleDetails,
    hasModuleAccess,
    hasPermission,
    isModuleAvailable,
    canAccessModule,
    login,
    logout,
    register,
    updateProfile,
    refreshUserData,
    loading,
    isAuthenticated
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
