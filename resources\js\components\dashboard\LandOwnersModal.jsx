// components/landOwners/LandOwnersModal.jsx
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import ProfileImageUpload from '@/components/ui/ProfileImageUpload';
import DocumentUpload from '@/components/ui/DocumentUpload';
import { Users, Edit, X } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

const LandOwnersModal = ({
  isOpen,
  onClose,
  onSubmit,
  formData,
  setFormData,
  isSubmitting = false,
  mode = 'add', // 'add' or 'edit'
  showAddForm,
  setShowAddForm,
  showEditForm,
  setShowEditForm,
  showLogModal,
  setShowLogModal
}) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const title = mode === 'add' ? t('landOwners.forms.addTitle') : t('landOwners.forms.editTitle');
  const description = mode === 'add' ? t('landOwners.forms.addDescription') : t('landOwners.forms.editDescription');
  const icon = mode === 'add' ? <Users className="h-6 w-6" /> : <Edit className="h-6 w-6" />;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            {icon}
            <div>
              <h3 className="text-lg font-semibold">{title}</h3>
              <p className="text-blue-100 text-sm">{description}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <form onSubmit={onSubmit} className="space-y-6">
            <style jsx>{`
              .no-focus-outline input {
                transition: all 0.2s ease-in-out;
                transform: scale(1);
              }
              .no-focus-outline input:focus {
                outline: none !important;
                border-color: #d1d5db !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                transform: scale(1.02) !important;
              }
              .no-focus-outline input:focus-visible {
                outline: none !important;
                border-color: #d1d5db !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                transform: scale(1.02) !important;
              }
              .no-focus-outline input:hover {
                border-color: #9ca3af !important;
                transform: scale(1.01) !important;
              }
            `}</style>

            {/* Profile Photo */}
            <div className="border-b pb-6">
              <ProfileImageUpload
                value={formData.photo}
                onChange={(file) => handleInputChange('photo', file)}
                name={`${formData.first_name} ${formData.last_name}`.trim() || 'Owner'}
                disabled={isSubmitting}
              />
            </div>

            {/* Basic Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">{t('landOwners.forms.basicInfo')}</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="first_name">{t('landOwners.forms.firstNameLabel')} {t('landOwners.forms.required')}</Label>
                  <Input
                    id="first_name"
                    value={formData.first_name}
                    onChange={(e) => handleInputChange('first_name', e.target.value)}
                    placeholder={t('landOwners.forms.firstNamePlaceholder')}
                    required
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="last_name">{t('landOwners.forms.lastNameLabel')} {t('landOwners.forms.required')}</Label>
                  <Input
                    id="last_name"
                    value={formData.last_name}
                    onChange={(e) => handleInputChange('last_name', e.target.value)}
                    placeholder={t('landOwners.forms.lastNamePlaceholder')}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="father_name">{t('landOwners.forms.fatherNameLabel')} {t('landOwners.forms.required')}</Label>
                <Input
                  id="father_name"
                  value={formData.father_name}
                  onChange={(e) => handleInputChange('father_name', e.target.value)}
                  placeholder={t('landOwners.forms.fatherNamePlaceholder')}
                  required
                />
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="mother_name">{t('landOwners.forms.motherNameLabel')}</Label>
                <Input
                  id="mother_name"
                  value={formData.mother_name}
                  onChange={(e) => handleInputChange('mother_name', e.target.value)}
                  placeholder={t('landOwners.forms.motherNamePlaceholder')}
                />
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="address">{t('landOwners.forms.addressLabel')} {t('landOwners.forms.required')}</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder={t('landOwners.forms.addressPlaceholder')}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="phone">{t('landOwners.forms.phoneLabel')}</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder={t('landOwners.forms.phonePlaceholder')}
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="nid_number">{t('landOwners.forms.nidLabel')}</Label>
                  <Input
                    id="nid_number"
                    value={formData.nid_number}
                    onChange={(e) => handleInputChange('nid_number', e.target.value)}
                    placeholder={t('landOwners.forms.nidPlaceholder')}
                  />
                </div>
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="email">{t('landOwners.forms.emailLabel')}</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder={t('landOwners.forms.emailPlaceholder')}
                />
              </div>
            </div>

            {/* Document Upload */}
            <div className="border-t pt-6">
              <DocumentUpload
                documentType={formData.document_type}
                onDocumentTypeChange={(type) => handleInputChange('document_type', type)}
                nidFront={formData.nid_front}
                onNidFrontChange={(file) => handleInputChange('nid_front', file)}
                nidBack={formData.nid_back}
                onNidBackChange={(file) => handleInputChange('nid_back', file)}
                passportPhoto={formData.passport_photo}
                onPassportPhotoChange={(file) => handleInputChange('passport_photo', file)}
                disabled={isSubmitting}
              />
            </div>

            {/* Buttons */}
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
                {t('landOwners.forms.cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting} className="bg-blue-600 hover:bg-blue-700 transition-colors">
                {isSubmitting ? (mode === 'add' ? t('landOwners.forms.creating') : t('landOwners.forms.updating')) :
                  (mode === 'add' ? t('landOwners.forms.create') : t('landOwners.forms.update'))}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LandOwnersModal;
