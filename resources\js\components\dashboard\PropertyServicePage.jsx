import React, { useState, useEffect } from 'react';
// import axios from 'axios';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

import {
  Search,
  Plus,
  Edit2,
  Trash2,
  Power,
  MoreHorizontal,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import propertyServiceAPI from '../../services/propertyServiceAPI';

export default function PropertyServicePage() {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [editService, setEditService] = useState(null);
  const [form, setForm] = useState({ name: '', description: '', is_active: true });
  const [saving, setSaving] = useState(false);


    // Fetch all services using propertyServiceAPI
    const fetchServices = async () => {
      setLoading(true);
      try {
        const data = await propertyServiceAPI.getall();
        setServices(data);
      } catch (err) {
        setError(err.message || 'Failed to fetch services');
      } finally {
        setLoading(false);
      }
    };

    useEffect(() => {
      fetchServices();
    }, []);

    // Create or update service
    const handleSave = async () => {
      setSaving(true);
      try {
        if (editService) {
          await propertyServiceAPI.update(editService.id, form);
        } else {
          await propertyServiceAPI.create(form);
        }
        setModalOpen(false);
        setForm({ name: '', description: '', is_active: true });
        setEditService(null);
        fetchServices();
      } catch (err) {
        setError(err.message || 'Failed to save service');
      } finally {
        setSaving(false);
      }
    };

    // Edit handler
    const handleEdit = (service) => {
      setEditService(service);
      setForm({
        name: service.name,
        description: service.description,
        is_active: service.is_active,
      });
      setModalOpen(true);
    };

    // Delete handler
    const handleDelete = async (service) => {
      if (window.confirm(`Delete service "${service.name}"?`)) {
        setSaving(true);
        try {
          await propertyServiceAPI.delete(service.id);
          fetchServices();
        } catch (err) {
          setError(err.message || 'Failed to delete service');
        } finally {
          setSaving(false);
        }
      }
    };

    // Toggle status handler
    const handleToggleStatus = async (service) => {
      setSaving(true);
      try {
        await propertyServiceAPI.toggleStatus(service.id);
        fetchServices();
      } catch (err) {
        setError(err.message || 'Failed to toggle status');
      } finally {
        setSaving(false);
      }
    };

    // Open modal for create
    const handleCreate = () => {
      setEditService(null);
      setForm({ name: '', description: '', is_active: true });
      setModalOpen(true);
    };

  // Filtered services for search and status
  const filteredServices = services.filter((service) => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && service.is_active) ||
      (statusFilter === 'inactive' && !service.is_active);
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Add/Edit Modal - Styled like UnitTypeModal */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] flex flex-col overflow-hidden">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
              <div>
                <h3 className="text-lg font-semibold">
                  {editService ? 'Edit Property Service' : 'Add Property Service'}
                </h3>
                <p className="text-blue-100 text-sm">
                  {editService ? 'Update property service information' : 'Create a new property service'}
                </p>
              </div>
              <button
                onClick={() => setModalOpen(false)}
                className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
              >
                ×
              </button>
            </div>
            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {error && <p className="text-red-600 mb-4">{error}</p>}
              <form className="space-y-4" onSubmit={e => { e.preventDefault(); handleSave(); }}>
                <div className="space-y-2">
                  <label htmlFor="name" className="block font-medium">Service Name *</label>
                  <input
                    id="name"
                    type="text"
                    value={form.name}
                    onChange={e => setForm({ ...form, name: e.target.value })}
                    placeholder="e.g., Rent, Sale, Lease"
                    required
                    disabled={saving}
                    className="border rounded px-2 py-1 w-full"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="description" className="block font-medium">Description</label>
                  <textarea
                    id="description"
                    value={form.description}
                    onChange={e => setForm({ ...form, description: e.target.value })}
                    placeholder="Brief description of this service"
                    rows={3}
                    disabled={saving}
                    className="border rounded px-2 py-1 w-full"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    id="is_active"
                    type="checkbox"
                    checked={form.is_active}
                    onChange={e => setForm({ ...form, is_active: e.target.checked })}
                    disabled={saving}
                    className="rounded"
                  />
                  <label htmlFor="is_active" className="font-medium">Active Status</label>
                </div>
                <div className="flex justify-end space-x-2 mt-6">
                  <button
                    type="button"
                    onClick={() => setModalOpen(false)}
                    className="px-4 py-2 bg-gray-300 rounded"
                    disabled={saving}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded"
                    disabled={saving}
                  >
                    {saving ? (editService ? 'Updating...' : 'Creating...') : (editService ? 'Update Service' : 'Create Service')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Property Services</h1>
          <p className="text-muted-foreground">
            Manage your property-related services
          </p>
        </div>
        <Button className="gap-2" onClick={handleCreate}>
          <Plus className="h-4 w-4" />
          Add Service
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search services..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Services List */}
      {filteredServices.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredServices.map((service) => (
            <Card key={service.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="font-semibold text-lg text-gray-900">
                    {service.name}
                  </h3>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => alert(`View details for ${service.name}`)}>
                        <Settings className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(service)}>
                        <Edit2 className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleToggleStatus(service)}>
                        <Power className="mr-2 h-4 w-4" />
                        {service.is_active ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      {/* <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDelete(service)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem> */}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  {service.description || 'No description provided.'}
                </p>
                <Badge
                  variant={service.is_active ? 'default' : 'secondary'}
                  className={service.is_active ? 'bg-green-500' : 'bg-gray-500'}
                >
                  {service.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No services found
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all'
                ? 'No services match your current filters.'
                : 'Get started by creating your first service.'}
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button className="gap-2" onClick={handleCreate}>
                <Plus className="h-4 w-4" />
                Add Service
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
