import React, { useState, useEffect } from 'react';
// import axios from 'axios';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import PropertyServicesSearch from './PropertyServicesSearch';
import PropertyServiceTable from './PropertyServiceTable';
import PropertyServiceModal from './PropertyServiceModal';
import {
  Plus,
  Settings
} from 'lucide-react';
import propertyServiceAPI from '../../services/propertyServiceAPI';

export default function PropertyServicePage() {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [editService, setEditService] = useState(null);
  const [form, setForm] = useState({ name: '', description: '', is_active: true });
  const [saving, setSaving] = useState(false);


    // Fetch all services using propertyServiceAPI
    const fetchServices = async () => {
      setLoading(true);
      try {
        const data = await propertyServiceAPI.getall();
        setServices(data);
      } catch (err) {
        setError(err.message || 'Failed to fetch services');
      } finally {
        setLoading(false);
      }
    };

    useEffect(() => {
      fetchServices();
    }, []);

    // Create or update service
    const handleSave = async () => {
      setSaving(true);
      try {
        if (editService) {
          await propertyServiceAPI.update(editService.id, form);
        } else {
          await propertyServiceAPI.create(form);
        }
        setModalOpen(false);
        setForm({ name: '', description: '', is_active: true });
        setEditService(null);
        fetchServices();
      } catch (err) {
        setError(err.message || 'Failed to save service');
      } finally {
        setSaving(false);
      }
    };

    // Edit handler
    const handleEdit = (service) => {
      setEditService(service);
      setForm({
        name: service.name,
        description: service.description,
        is_active: service.is_active,
      });
      setModalOpen(true);
    };

    // Delete handler
    const handleDelete = async (service) => {
      if (window.confirm(`Delete service "${service.name}"?`)) {
        setSaving(true);
        try {
          await propertyServiceAPI.delete(service.id);
          fetchServices();
        } catch (err) {
          setError(err.message || 'Failed to delete service');
        } finally {
          setSaving(false);
        }
      }
    };

    // Toggle status handler
    const handleToggleStatus = async (service) => {
      setSaving(true);
      try {
        await propertyServiceAPI.toggleStatus(service.id);
        fetchServices();
      } catch (err) {
        setError(err.message || 'Failed to toggle status');
      } finally {
        setSaving(false);
      }
    };

    // Open modal for create
    const handleCreate = () => {
      setEditService(null);
      setForm({ name: '', description: '', is_active: true });
      setModalOpen(true);
    };

  // Filtered services for search and status
  const filteredServices = services.filter((service) => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && service.is_active) ||
      (statusFilter === 'inactive' && !service.is_active);
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Add/Edit Modal - Styled like UnitTypeModal */}
      {modalOpen && (
          <PropertyServiceModal
           editService = {editService}
          error = {error}
         form = {form}
         setForm = {setForm}
       saving = {saving}
        handleSave = {handleSave}
        setModalOpen = {setModalOpen}
          />
      )}
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Property Services</h1>
          <p className="text-muted-foreground">
            Manage your property-related services
          </p>
        </div>
        <Button className="gap-2" onClick={handleCreate}>
          <Plus className="h-4 w-4" />
          Add Service
        </Button>
      </div>

      {/* Filters */}
        <PropertyServicesSearch
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        setSearchTerm={setSearchTerm}
        setStatusFilter={setStatusFilter}
      />

      {/* Services List */}
      {filteredServices.length > 0 ? (
        <PropertyServiceTable
          filteredServices={filteredServices}
        handleEdit={handleEdit}
          handleToggleStatus={handleToggleStatus}
          
        />
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No services found
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all'
                ? 'No services match your current filters.'
                : 'Get started by creating your first service.'}
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button className="gap-2" onClick={handleCreate}>
                <Plus className="h-4 w-4" />
                Add Service
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
