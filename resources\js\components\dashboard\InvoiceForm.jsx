import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Plus } from "lucide-react";

const InvoiceForm = ({
  isOpen,
  onClose,
  onSave,
  editInvoice,
  saving,
  form,
  setForm,
  properties,
  tenants,
  customers,
  paymentMethods,
  paymentStatuses,
  paymentTypes,
  propertyServices,
  propertyItems,
  setPropertyItems,
  items,
  setItems,
  units,
  unitLoading,
  propertyUnits,
  fetchUnitsForPropertyItem,
  updatePropertyItem,
  addPropertyItem,
  removePropertyItem,
  updateItem,
  addItem,
  removeItem,
  customerSearch,
  setCustomerSearch
}) => {
  const [localCustomerSearch, setLocalCustomerSearch] = useState("");
  const [localPropertySearch, setLocalPropertySearch] = useState("");
  useEffect(() => {
    setLocalCustomerSearch(customerSearch);
  }, [customerSearch]);

  // Add CSS to remove blue border on focus
  const styles = `
    .no-focus-outline input:focus,
    .no-focus-outline select:focus {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
    }
    .no-focus-outline input:hover,
    .no-focus-outline select:hover {
      border-color: #9ca3af !important;
    }
  `;

  const handleCustomerSearchChange = (e) => {
    setLocalCustomerSearch(e.target.value);
    setCustomerSearch(e.target.value);
  };

  const handlePropertySearchChange = (e) => {
    setLocalPropertySearch(e.target.value);
    setLocalPropertySearch(e.target.value);
  };

  const calculateGrandTotal = (subtotal, discount, is_percentage) => {
    const parsedDiscount = parseFloat(discount) || 0.00;
    let grandTotal = subtotal;

    if (is_percentage === 1) {
      const discountAmount = (subtotal * parsedDiscount) / 100;
      grandTotal = subtotal - discountAmount;
    } else {
      grandTotal = subtotal - parsedDiscount;
    }

    return grandTotal > 0 ? grandTotal.toFixed(2) : "0.00";
  };
  const grandTotal=form.grand_total|| 0.00;

  const propertySubtotal = propertyItems.reduce((acc, item) => {
    const total = parseFloat(item.total_amount) || 0;
    return acc + total;
  }, 0);

  const itemsSubtotal = items.reduce((acc, item) => {
    const total = parseFloat(item.item_total_price) || 0;
    return acc + total;
  }, 0);

  const subtotal = propertySubtotal + itemsSubtotal;

  useEffect(() => {
    const newGrandTotal = calculateGrandTotal(subtotal, form.discount, form.is_percentage);
    setForm(prev => ({ ...prev, grand_total: newGrandTotal }));
  }, [subtotal, form.discount, form.is_percentage]);

  // Block scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup function to restore scrolling when component unmounts
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 backdrop-blur-sm h-full w-full z-50 flex items-center justify-center">
      <style>{styles}</style>
      <div className="p-5 border w-full max-w-6xl shadow-lg rounded-md bg-white mx-auto max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b">
          <h3 className="text-lg font-semibold text-gray-900">{editInvoice ? "Edit Invoice" : "Create New Invoice"}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <span className="sr-only">Close</span>
            <svg
              className="h-6 w-6"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        {/* Form */}
        <div className="grid gap-4 mt-4">
          <div className="grid grid-cols-1">
            <div className="flex flex-col items-center">
              <RadioGroup
                value={form.invoice_type}
                onValueChange={(value) => setForm({ ...form, invoice_type: value })}
                className="flex gap-6"
              >
                {propertyServices.map((service) => (
                  <label
                    key={service.id}
                    htmlFor={`invoice-type-${service.id}`}
                    className="relative flex items-center w-64 p-2 rounded-2xl border shadow-md cursor-pointer transition p-[10px] m-0 shadow-none pt-[5px] pb-[4px]"
                  >
                    <RadioGroupItem
                      value={service.id.toString()}
                      id={`invoice-type-${service.id}`}
                      className="absolute top-3 right-3"
                    />
                    <img
                      src={service.icon}
                      alt={`${service.name} icon`}
                      className="h-8 w-8 object-contain mr-4 flex-shrink-0"
                    />
                    <div className="flex flex-col justify-center">
                      <span className="text-lg font-bold text-left">{service.name}</span>
                      <p className="!text-[13px] text-sm text-gray-500 text-left">{service.description}</p>
                    </div>
                  </label>
                ))}
              </RadioGroup>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4">
            {form.invoice_type === "1" && (
              <div>
                <Label htmlFor="tenant_id">Tenant</Label>
                <Select
                  value={form.tenant_id?.toString() || ""}
                  onValueChange={value => {
                    setForm(prev => ({
                      ...prev,
                      tenant_id: value,
                    }));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select tenant" />
                  </SelectTrigger>
                  <SelectContent>
                    {tenants.map(tenant => (
                      <SelectItem key={tenant.id} value={tenant.id.toString()}>
                        {tenant.first_name && tenant.last_name
                          ? `${tenant.first_name} ${tenant.last_name} - T${tenant.id}`
                          : tenant.name
                          ? `${tenant.name} - T${tenant.id}`
                          : `Tenant ${tenant.id} - T${tenant.id}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            
            {form.invoice_type && form.invoice_type !== "1" && form.invoice_type !== "8" ? (
              <div>
                <Label htmlFor="customer_id">Customer</Label>
                <Select
                  value={form.customer_id?.toString() || ""}
                  onValueChange={value => {
                    setForm(prev => ({
                      ...prev,
                      customer_id: value,
                    }));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select customer" />
                  </SelectTrigger>
                  <SelectContent>
                    <div className="p-2">
                      <Input
                        placeholder="Search customers..."
                        className="mb-2"
                        value={localCustomerSearch}
                        onChange={handleCustomerSearchChange}
                      />
                    </div>
                    {customers
                      .filter(customer => 
                        customer.name?.toLowerCase().includes(localCustomerSearch.toLowerCase()) || 
                        customer.email?.toLowerCase().includes(localCustomerSearch.toLowerCase()) ||
                        customer.id.toString().includes(localCustomerSearch)
                      )
                      .map(customer => (
                        <SelectItem
                          key={customer.id}
                          value={customer.id.toString()}
                        >
                          {`${customer.name || customer.email}- C${customer.id}`}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            ) : null}

            {form.invoice_type === "8" && (
              <div>
                <Label htmlFor="tenant_id">Customer & Tenant</Label>
                <Select
                  value={form.tenant_id?.toString() || ""}
                  onValueChange={value => {
                    setForm(prev => ({
                      ...prev,
                      tenant_id: value,
                    }));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select tenant or customer" />
                  </SelectTrigger>
                  <SelectContent>
                    <div className="p-2">
                      <Input
                        placeholder="Search customers or tenants..."
                        className="mb-2"
                        value={localCustomerSearch}
                        onChange={handlePropertySearchChange}
                      />
                    </div>
                    {[...tenants, ...customers]
                      .filter(person => 
                        (person.name?.toLowerCase().includes(localCustomerSearch.toLowerCase()) || 
                         person.email?.toLowerCase().includes(localCustomerSearch.toLowerCase()) ||
                         person.first_name?.toLowerCase().includes(localCustomerSearch.toLowerCase()) ||
                         person.last_name?.toLowerCase().includes(localCustomerSearch.toLowerCase()) ||
                         person.id.toString().includes(localCustomerSearch))
                      )
                      .map(person => (
                        <SelectItem key={person.id} value={person.id.toString()}>
                          {person.first_name && person.last_name
                            ? `${person.first_name} ${person.last_name} - T${person.id}`
                            : person.name
                            ? `${person.name} - ${person.email ? 'C' : 'T'}${person.id}`
                            : person.email
                            ? `${person.email} - C-${person.id}`
                            : `Person ${person.id}`}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="no-focus-outline">
              <Label htmlFor="invoice_number">Invoice Number</Label>
              <Input
                id="invoice_number"
                value={form.invoice_number}
                onChange={(e) => setForm({ ...form, invoice_number: e.target.value })}
                placeholder="INV-001"
              />
            </div>

            <div className="no-focus-outline">
              <Label htmlFor="invoice_date">Invoice Date</Label>
              <Input
                id="invoice_date"
                type="date"
                value={form.invoice_date}
                onChange={(e) => setForm({ ...form, invoice_date: e.target.value })}
              />
            </div>

            <div className="no-focus-outline">
              <Label htmlFor="due_date">Due Date</Label>
              <Input
                id="due_date"
                type="date"
                value={form.due_date}
                onChange={(e) => setForm({ ...form, due_date: e.target.value })}
              />
            </div>
          </div>
          <style>
        {`
          /* Hide number input spinners */
          .no-spinner::-webkit-outer-spin-button,
          .no-spinner::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }

          .no-spinner {
            -moz-appearance: textfield; /* Firefox */
          }
        `}
      </style>

          {/* Property Items Section */}
          {form.invoice_type && form.invoice_type !== "8" && (
            <div className="mt-6">
              <table className="min-w-full border-0 rounded-lg shadow-sm">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="w-[30%] p-[0.2rem] text-left border">Property</th>
                    <th className="w-[20%] p-[0.2rem] text-left border">Unit</th>
                    <th className="w-[15%] p-[0.2rem] text-left border">Rate</th>
                    <th className="w-[10%] p-[0.2rem] text-left border">Tax (%)</th>
                    <th className="w-[15%] p-[0.2rem] text-left border">Amount</th>
                    <th className="w-[10%] p-[0.2rem] text-center border">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {propertyItems.map((item, idx) => (
                    <tr key={idx} className="border-b">
                      <td className="p-2 border">
                        <Select
                          value={item.property_id}
                          onValueChange={value => {
                            updatePropertyItem(idx, "property_id", value);
                            fetchUnitsForPropertyItem(value, idx);
                            const selectedProperty = properties.find(p => p.id.toString() === value);
                            setForm(prev => ({
                              ...prev,
                              property_id: value,
                            }));
                            if (selectedProperty && (!propertyUnits[idx] || propertyUnits[idx].length === 0)) {
                              updatePropertyItem(idx, "amount", selectedProperty.total_price ? selectedProperty.total_price.toString() : "");
                            } else {
                              updatePropertyItem(idx, "amount", "");
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select property" />
                          </SelectTrigger>
                          <SelectContent>
                            
                            <div className="p-2">
                              <Input
                                placeholder="Search Property..."
                                className="mb-2"
                                value={localPropertySearch}
                                onChange={handlePropertySearchChange}
                              />
                            </div>
                            {properties
                            .filter(property => 
                                  property.name?.toLowerCase().includes(localPropertySearch.toLowerCase()) || 
                                  property.title?.toLowerCase().includes(localPropertySearch.toLowerCase()) ||
                                  property.id.toString().includes(localPropertySearch)
                                )
                            .map(property => (
                              <SelectItem key={property.id} value={property.id.toString()}>
                                {property.title || `Property ${property.id}`}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </td>

                      <td className="p-2 border">
                        {item.property_id && propertyUnits[idx] && propertyUnits[idx].length > 0 ? (
                          <Select
                            value={item.unit_id}
                            onValueChange={value => {
                              updatePropertyItem(idx, "unit_id", value);
                              const selectedUnit = propertyUnits[idx].find(u => u.id.toString() === value);
                              if (selectedUnit) {
                                let price = 0;
                                if (form.invoice_type === '1' && selectedUnit.rent_price != null) {
                                  price = selectedUnit.rent_price;
                                } else if (form.invoice_type === '2' && selectedUnit.lease_price != null) {
                                  price = selectedUnit.lease_price;
                                } else if (form.invoice_type === '3' && selectedUnit.sell_price != null) {
                                  price = selectedUnit.sell_price;
                                } else {
                                  price = selectedUnit.sell_price ?? selectedUnit.rent_price ?? selectedUnit.lease_price ?? 0;
                                }
                                updatePropertyItem(idx, "amount", price.toString());
                              }
                            }}
                            disabled={unitLoading}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder={unitLoading ? "Loading units..." : "Select unit"} />
                            </SelectTrigger>
                            <SelectContent>
                              {propertyUnits[idx].map(unit => (
                                <SelectItem key={unit.id} value={unit.id.toString()}>
                                  {unit.unit_number ? `Unit ${unit.unit_number}` : `Unit ${unit.id}`}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <span className="text-gray-400">No units</span>
                        )}
                      </td>

                      <td className="p-2 border no-focus-outline">
                        <Input
                          type="number"
                          step="0.01"
                          value={item.amount}
                          onChange={e => updatePropertyItem(idx, "amount", e.target.value)}
                          placeholder="0.00"
                           className="no-spinner"
                        />
                      </td>

                      <td className="p-2 border no-focus-outline">
                        <Input
                          type="number"
                          step="0.01"
                          value={item.tax_amount}
                          onChange={e => updatePropertyItem(idx, "tax_amount", e.target.value)}
                          placeholder="0.00"
                          className="no-spinner"
                        />
                      </td>

                      <td className="p-2 border no-focus-outline">
                        <Input
                          type="number"
                          step="0.01"
                          value={item.total_amount}
                          onChange={e => updatePropertyItem(idx, "total_amount", e.target.value)}
                          placeholder="0.00"
                          className="no-spinner"
                        />
                      </td>

                      <td className="p-2 text-center border">
                        {propertyItems.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removePropertyItem(idx)}
                            className="text-red-600 text-sm underline"
                          >
                            Remove
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              <button
                type="button"
                onClick={addPropertyItem}
                className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg"
              >
                + Add Property
              </button>
            </div>
          )}



             {form.invoice_type && form.invoice_type == "8" && (
            <div className="mt-6">
              <table className="min-w-full border-0 rounded-lg shadow-sm">
               <thead>
                  <tr className="bg-gray-100 text-left">
                    <th className="p-[0.2rem] w-[30%]  border">Item Name</th>
                    <th className="p-[0.2rem] w-[20%] border text-center">Qty</th>
                    <th className="p-[0.2rem] w-[15%] border text-center">Price</th>
                    <th className="p-[0.2rem] w-[10%] border text-center">Tax</th>
                    <th className="p-[0.2rem] w-[15%] border text-center">Total Price</th>
                    <th className="p-[0.2rem] w-[10%] border text-center">Action</th>
                  </tr>
                </thead>
               <tbody>
                  {items.map((item, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="p-2 border no-focus-outline">
                        <Input
                          id={`item_name_${index}`}
                          value={item.item_name}
                          onChange={e => updateItem(index, "item_name", e.target.value)}
                          placeholder="Product 1"
                          className="w-full"
                        />
                      </td>

                      <td className="p-2 border no-focus-outline">
                        <Input
                          id={`qty_${index}`}
                          type="number"
                          min="1"
                          value={item.qty}
                          onChange={e => updateItem(index, "qty", e.target.value)}
                          className="text-center w-full no-spinner"
                        />
                      </td>

                      <td className="p-2 border no-focus-outline">
                        <Input
                          id={`item_price_${index}`}
                          type="number"
                          min="0"
                          value={item.item_price}
                          onChange={e => updateItem(index, "item_price", e.target.value)}
                          className="text-center w-full no-spinner"
                        />
                      </td>

                      <td className="p-2 border no-focus-outline">
                        <Input
                          id={`item_tax_${index}`}
                          type="number"
                          min="0"
                          value={item.item_tax}
                          onChange={e => updateItem(index, "item_tax", e.target.value)}
                          className="text-center w-full no-spinner"
                        />
                      </td>

                      <td className="p-2 border no-focus-outline">
                        <Input
                          id={`item_total_price_${index}`}
                          type="number"
                          value={item.item_total_price}
                          readOnly
                          className="text-center w-full bg-gray-50 no-spinner"
                        />
                      </td>

                      <td className="p-2 border text-center ">
                        {items.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeItem(index)}
                            className="text-red-600 text-sm underline"
                          >
                            Remove
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              <button
                type="button"
                 onClick={addItem}
                className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg"
              >
                + Add Item
              </button>
            </div>
          )}

         
          {/* Payment and Summary Section */}
          <div className="grid grid-cols-[3fr_1fr] gap-4">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="payment_method_id">Payment Method</Label>
                <Select value={form.payment_method_id} onValueChange={value => setForm(prev => ({ ...prev, payment_method_id: value }))}>
                  <SelectTrigger><SelectValue placeholder="Select method" /></SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map(method => <SelectItem key={method.id} value={method.id.toString()}>{method.name}</SelectItem>)}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="payment_Type_id">Payment Type</Label>
                <Select value={form.payment_Type_id} onValueChange={value => setForm(prev => ({ ...prev, payment_Type_id: value }))}>
                  <SelectTrigger><SelectValue placeholder="Select Payment Type" /></SelectTrigger>
                  <SelectContent>
                    {paymentTypes.map(type => <SelectItem key={type.id} value={type.id.toString()}>{type.name}</SelectItem>)}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="payment_status_id">Status</Label>
                <Select className="no-focus-outline " value={form.payment_status_id} onValueChange={value => setForm(prev => ({ ...prev, payment_status_id: value }))}>
                  <SelectTrigger><SelectValue placeholder="Select status" /></SelectTrigger>
                  <SelectContent>
                    {paymentStatuses.map(status => <SelectItem key={status.id} value={status.id.toString()}>{status.name}</SelectItem>)}
                  </SelectContent>
                </Select>
              </div>

              <div className="col-span-3">
                <Label htmlFor="notes">Notes</Label>
                <Textarea id="notes" className="no-focus-outline" value={form.notes} onChange={e => setForm(prev => ({ ...prev, notes: e.target.value }))} rows={3} placeholder="Additional notes..." />
              </div>

              <div className="col-span-3">
                <Accordion type="single" collapsible>
                  <AccordionItem value="item-1">
                    <AccordionTrigger>+ Add Terms And Conditions</AccordionTrigger>
                    <AccordionContent>
                      <Textarea id="terms_and_conditions" value={form.terms_and_conditions} onChange={e => setForm(prev => ({ ...prev, terms_and_conditions: e.target.value }))} rows={3} placeholder="Terms And Conditions" />
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            </div>

            {/* Summary Table */}
            <table className="min-w-full border-0 rounded-lg shadow-sm">
              <tbody>
                <tr className="border-0">
                  <td className="p-2 font-medium text-gray-800">Sub Total</td>
                  <td className="p-2 text-right">${subtotal.toFixed(2)}</td>
                </tr>

                <tr className="border-0">
                  <td className="p-2 font-medium text-gray-800">Discount</td>
                  <td className="p-2 relative no-focus-outline">
                    <Input
                      id="discount"
                      type="number"
                      step="0.01"
                      value={form.discount}
                      onChange={e => setForm(prev => ({ ...prev, discount: e.target.value }))}
                      placeholder="Enter discount"
                      className="pr-20 w-full no-spinner"
                    />
                    <select
                      value={form.is_percentage}
                      onChange={e => setForm(prev => ({ ...prev, is_percentage: Number(e.target.value) }))}
                      className="absolute top-1/2 right-0 h-10 w-12 -translate-y-1/2 border-l border-gray-300 rounded-r px-2 bg-white"
                    >
                      <option value={1}>%</option>
                      <option value={0}>$</option>
                    </select>
                  </td>
                </tr>

                <tr>
                  <td className="p-2 font-medium text-gray-800">Grand Total</td>
                  <td className="p-2 text-right ">
                    ${grandTotal}
                    <Input
                      id="grand_total"
                      type="hidden"
                      step="0.01"
                      value={form.grand_total || ""}
                      readOnly
                      placeholder="0.00"
                      className="font-bold w-full"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          {/* Modal footer */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={onSave}
              disabled={saving || !form.invoice_number.trim() || (form.invoice_type === "1" && !form.tenant_id)}
            >
              {saving ? "Saving..." : editInvoice ? "Update" : "Create"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceForm;
