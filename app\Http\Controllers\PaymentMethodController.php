<?php

namespace App\Http\Controllers;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PaymentMethodController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try {
            $query = PaymentMethod::query();
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where('name', 'LIKE', "%{$search}%");
            }
            $perPage = $request->get('per_page', 10);
            $paymentMethods = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $paymentMethods
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment methods: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:payment_methods,name',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $paymentMethod = PaymentMethod::create($validated);
            return response()->json([
                'success' => true,
                'data' => $paymentMethod,
                'message' => 'Payment method created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        //
        try {
            $paymentMethod = PaymentMethod::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $paymentMethod,
                'message' => 'Payment method retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        //
        try {
            $paymentMethod = PaymentMethod::findOrFail($id);
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:payment_methods,name,' . $id,
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $paymentMethod->update($validated);
            return response()->json([
                'success' => true,
                'data' => $paymentMethod->fresh(),
                'message' => 'Payment method updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        //
        try {
            $paymentMethod = PaymentMethod::findOrFail($id);
            $paymentMethod->delete();
            return response()->json([
                'success' => true,
                'message' => 'Payment method deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    public function toggleStatus(string $id): JsonResponse
    {
        try{    
            $paymentMethod = PaymentMethod::findOrFail($id);
            $paymentMethod->is_active = !$paymentMethod->is_active;
            $paymentMethod->save();
            return response()->json([
                'success' => true,
                'data' => $paymentMethod,
                'message' => 'Payment method status updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment method status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function dropdown(): JsonResponse
    {
        try{    
            $paymentMethods = PaymentMethod::active()
                ->ordered()
                ->select('id', 'name', 'description')
                ->get();
            return response()->json([
                'success' => true,
                'data' => $paymentMethods,
                'message' => 'Payment methods for dropdown retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment methods for dropdown',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
