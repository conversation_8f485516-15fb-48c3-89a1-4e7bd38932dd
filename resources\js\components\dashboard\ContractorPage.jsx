import React, { useState, useEffect } from 'react';
import contractorAPI from '../../services/contractorAPI';
import Swal from 'sweetalert2';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { ContentModal, ContentModalContent, ContentModalHeader, ContentModalTitle, ContentModalOverlay } from '../ui/content-modal';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Textarea } from '../ui/textarea';
import {Plus, Edit2,Filter, HardHat, Phone, Mail, FileText, MapPin, Upload, X, Download } from 'lucide-react';
import ContractorStatistics from './ContractorStatistics';
import ContractorSearch from './ContractorSearch';
import ContractorTable from './ContractorTable';
import ContractorModal from './ContractorModal';

const ContractorPage = () => {
  const [contractors, setContractors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({});
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingContractor, setEditingContractor] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    trade_license: '',
    email: '',
    address: '',
    status: 'active'
  });
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [existingFiles, setExistingFiles] = useState([]);

  // Pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortBy, setSortBy] = useState('id');
  const [sortOrder, setSortOrder] = useState('desc');

  // Fetch contractors
  const fetchContractors = async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        per_page: perPage,
        search: searchTerm,
        status: statusFilter,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      const response = await contractorAPI.getContractors(params);
      setContractors(response.data.data.data);
      setCurrentPage(response.data.data.current_page);
      setTotalPages(response.data.data.last_page);
    } catch (error) {
      console.error('Error fetching contractors:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to fetch contractors',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await contractorAPI.getContractorStatistics();
      setStatistics(response.data.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  useEffect(() => {
    fetchContractors(1);
    fetchStatistics();
  }, [perPage, searchTerm, statusFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    fetchContractors(1);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      phone: '',
      trade_license: '',
      email: '',
      address: '',
      status: 'active'
    });
    setSelectedFiles([]);
    setExistingFiles([]);
  };

  // Handle file selection
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(prevFiles => [...prevFiles, ...files]);
  };

  // Remove selected file
  const removeSelectedFile = (index) => {
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  // Remove existing file
  const removeExistingFile = (index) => {
    setExistingFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  // Handle add contractor
  const handleAddContractor = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.name.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Name is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.phone.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Phone is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.trade_license.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Trade License is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.email.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Email is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    
    try {
      const formDataToSend = new FormData();
      
      // Add fields
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== '') {
          formDataToSend.append(key, formData[key]);
        }
      });

      // Add files
      selectedFiles.forEach((file, index) => {
        formDataToSend.append(`files[${index}]`, file);
      });

      await contractorAPI.createContractor(formDataToSend);
      
      // Close modal and reset form first
      setShowAddModal(false);
      resetForm();
      fetchContractors();
      fetchStatistics();
      
      // Show success message
      Swal.fire({
        title: 'Success!',
        text: 'Contractor created successfully',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error creating contractor:', error);
      Swal.fire({
        title: 'Error!',
        text: error.response?.data?.message || 'Failed to create contractor',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  // Handle edit contractor
  const handleEditContractor = (contractor) => {
    setEditingContractor(contractor);
    setFormData({
      name: contractor.name || '',
      phone: contractor.phone || '',
      trade_license: contractor.trade_license || '',
      email: contractor.email || '',
      address: contractor.address || '',
      status: contractor.status || 'active'
    });
    setSelectedFiles([]);
    setExistingFiles(contractor.files || []);
    setShowEditModal(true);
  };

  // Handle update contractor
  const handleUpdateContractor = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.name.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Name is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.phone.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Phone is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.trade_license.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Trade License is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.email.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Email is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    
    try {
      const formDataToSend = new FormData();
      
      // Add fields
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== '') {
          formDataToSend.append(key, formData[key]);
        }
      });

      // Add new files
      selectedFiles.forEach((file, index) => {
        formDataToSend.append(`files[${index}]`, file);
      });

      // Add existing files to keep as an array
      existingFiles.forEach((file, index) => {
        const fileId = file.id || file;
        formDataToSend.append(`existing_files[]`, fileId);
      });

      console.log('Sending contractor update request:', {
        contractorId: editingContractor.id,
        formDataEntries: Array.from(formDataToSend.entries())
      });

      await contractorAPI.updateContractor(editingContractor.id, formDataToSend);
      
      // Close modal and reset form first
      setShowEditModal(false);
      setEditingContractor(null);
      resetForm();
      fetchContractors();
      fetchStatistics();
      
      // Show success message
      Swal.fire({
        title: 'Success!',
        text: 'Contractor updated successfully',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error updating contractor:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error headers:', error.response?.headers);
      
      let errorMessage = 'Failed to update contractor';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        const errorMessages = Object.values(error.response.data.errors).flat();
        errorMessage = errorMessages.join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Swal.fire({
        title: 'Error!',
        text: errorMessage,
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  // Handle delete contractor
  const handleDeleteContractor = async (contractor) => {
    const result = await Swal.fire({
      title: 'Delete Contractor',
      text: `Are you sure you want to delete ${contractor.name}? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      try {
        await contractorAPI.deleteContractor(contractor.id);
        fetchContractors();
        fetchStatistics();
        
        Swal.fire({
          title: 'Deleted!',
          text: 'Contractor has been deleted successfully.',
          icon: 'success',
          timer: 2000,
          showConfirmButton: false
        });
      } catch (error) {
        console.error('Error deleting contractor:', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete contractor',
          icon: 'error',
          confirmButtonColor: '#ef4444'
        });
      }
    }
  };

  // Handle status toggle
  const handleStatusToggle = async (contractor) => {
    const newStatus = contractor.status === 'active' ? 'inactive' : 'active';
    
    try {
      const formData = new FormData();
      formData.append('name', contractor.name);
      formData.append('phone', contractor.phone);
      formData.append('trade_license', contractor.trade_license);
      formData.append('email', contractor.email);
      formData.append('address', contractor.address || '');
      formData.append('status', newStatus);

      await contractorAPI.updateContractor(contractor.id, formData);
      fetchContractors();
      fetchStatistics();
      
      Swal.fire({
        title: 'Success!',
        text: `Contractor ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`,
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error toggling status:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to update contractor status',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <HardHat className="w-8 h-8 mr-3 text-blue-600" />
            Contractor Management
          </h1>
          <p className="text-gray-600 mt-1">Manage construction contractors and their business information</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => setShowAddModal(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Contractor
        </Button>
      </div>

      {/* Statistics Cards */}
    <ContractorStatistics statistics={statistics} />

      {/* Filters */}
      <Card className="border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b">
          <CardTitle className="flex items-center text-lg">
            <Filter className="w-5 h-5 mr-2 text-blue-600" />
            Search & Filter Contractors
          </CardTitle>
          <CardDescription>
            Find contractors by name, license number, contact information or status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ContractorSearch
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
            sortBy={sortBy}
            setSortBy={setSortBy}
            sortOrder={sortOrder}
            setSortOrder={setSortOrder}
            handleSearch={handleSearch}
          />
        </CardContent>
      </Card>

      {/* Contractors Table */}
      <ContractorTable
        contractors={contractors}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        fetchContractors={fetchContractors}
        handleEditContractor={handleEditContractor}
        handleStatusToggle={handleStatusToggle}
        handleDeleteContractor={handleDeleteContractor}
      />

      {/* Add Contractor Modal */}
      <ContractorModal
            open={showAddModal}
            onOpenChange={setShowAddModal}
            title="Add New Contractor"
            formData={formData}
            setFormData={setFormData}
            selectedFiles={selectedFiles}
            setSelectedFiles={setSelectedFiles}
            onSubmit={handleAddContractor}
            submitLabel="Add Contractor"
          />

 
          <ContractorModal
            open={showEditModal}
            onOpenChange={setShowEditModal}
            title="Edit Contractor"
            formData={formData}
            setFormData={setFormData}
            selectedFiles={selectedFiles}
            setSelectedFiles={setSelectedFiles}
            existingFiles={existingFiles}
            removeExistingFile={removeExistingFile}
            onSubmit={handleUpdateContractor}
            submitLabel="Update Contractor"
          />
    </div>
  );
};

export default ContractorPage;
