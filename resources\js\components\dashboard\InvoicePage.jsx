import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Plus } from "lucide-react";
import propertyServiceAPI from "@/services/propertyServiceAPI";
import projectAPI from "@/services/projectAPI";
import invoiceAPI from "@/services/invoiceAPI";
import tenantsAPI from "@/services/tenantsAPI";
import paymentMethodAPI from "@/services/paymentMethodAPI";
import paymentStatusAPI from "@/services/paymentStatusAPI";
import paymentTypeAPI from "@/services/paymentTypeAPI";
import customersAPI from "@/services/customersAPI";

import InvoiceStatistics from "./InvoiceStatistics";
import InvoiceFilters from "./InvoiceFilters";
import InvoiceTable from "./InvoiceTable";
import InvoiceForm from "./InvoiceForm";

function InvoicePage() {
  // State
  const [units, setUnits] = useState([]);
  const [unitLoading, setUnitLoading] = useState(false);
  const [properties, setProperties] = useState([]);
  const [invoices, setInvoices] = useState([]); 
  const [tenants, setTenants] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [paymentStatuses, setPaymentStatuses] = useState([]);
  const [paymentTypes, setPaymentTypes] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [propertyServices, setPropertyServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [modalOpen, setModalOpen] = useState(false);
  const [editInvoice, setEditInvoice] = useState(null);
  const [saving, setSaving] = useState(false);
  const [statistics, setStatistics] = useState({
    counts: {
      total: 0,
      paid: 0,
      pending: 0,
      overdue: 0,
      failed: 0,
      refunded: 0,
      cancelled: 0,
    },
    amounts: {
      total: 0,
      paid: 0,
      pending: 0,
      overdue: 0,
      failed: 0,
      refunded: 0,
      cancelled: 0,
    }
  });
  const [propertyItems, setPropertyItems] = useState([
    {
      property_id: "",
      unit_id: "",
      amount: "",
      tax_amount: "",
      total_amount: "",
    },
  ]);
  const [propertyUnits, setPropertyUnits] = useState({});
  const [customerSearch, setCustomerSearch] = useState("");
  const [items, setItems] = useState([
    { item_name: "", qty: 1, item_price: 0, item_tax: 0, item_total_price: 0 }
  ]);
  const [form, setForm] = useState({
    invoice_number: "",
    invoice_type: '3',
    tenant_id: "",
    property_id: "",
    customer_id: "",
    invoice_date: "",
    due_date: "",
    grand_total: "",
    discount: "",
    is_percentage: 0,
    billing_date: "",
    billing_end: "",
    amount: "",
    tax_amount: "",
    discount_amount: "",
    total_amount: "",
    payment_status_id: "",
    payment_method_id: "",
    payment_Type_id: "",
    payment_date: "",
    payment_method: "",
    paymentType: "",
    payment_amount: "",
    customer_email: "",
    customer_phone: "",
    customer_address: "",
    notes: ""
  });

  // Helper functions
  const fetchUnitsForPropertyItem = async (propertyId, idx) => {
    if (!propertyId) {
      setPropertyUnits(prev => ({ ...prev, [idx]: [] }));
      return;
    }
    setUnitLoading(true);
    try {
      const data = await projectAPI.unitDropdown(propertyId);
      setPropertyUnits(prev => ({
        ...prev,
        [idx]: Array.isArray(data.data) ? data.data : [],
      }));
    } catch {
      setPropertyUnits(prev => ({ ...prev, [idx]: [] }));
    } finally {
      setUnitLoading(false);
    }
  };

  const updatePropertyItem = (index, field, value) => {
    setPropertyItems(prev => {
      const updated = [...prev];
      updated[index][field] = value;

      if (field === "amount" || field === "tax_amount") {
        const base = parseFloat(updated[index].amount) || 0;
        const tax = parseFloat(updated[index].tax_amount) || 0;
        let total = base;
        if (tax > 0) {
          if (tax <= 100) total = base + (base * tax / 100);
          else total = base + tax;
        }
        updated[index].total_amount = total.toFixed(2);
      }

      return updated;
    });
  };

  const addPropertyItem = () => {
    setPropertyItems([
      ...propertyItems,
      { property_id: "", unit_id: "", amount: "", tax_amount: "", total_amount: "" },
    ]);
  };

  const removePropertyItem = (index) => {
    setPropertyItems(propertyItems.filter((_, i) => i !== index));
  };

  const updateItem = (index, field, value) => {
    setItems(prev => {
      const updated = [...prev];
      updated[index][field] = value;

      const qty = parseFloat(updated[index].qty) || 0;
      const price = parseFloat(updated[index].item_price) || 0;
      const tax = parseFloat(updated[index].item_tax) || 0;

      let total = qty * price;
      if (tax > 0) {
        if (tax <= 100) total += (total * tax) / 100;
        else total += tax;
      }

      updated[index].item_total_price = total.toFixed(2);

      return updated;
    });
  };

  const addItem = () => {
    setItems([...items, { item_name: "", qty: 1, item_price: 0, item_tax: 0, item_total_price: 0 }]);
  };

  const removeItem = (index) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const d = new Date(dateString);
    return d.toISOString().split("T")[0];
  };

  const calculateGrandTotal = (subtotal, discount, is_percentage) => {
    const parsedDiscount = parseFloat(discount) || 0;
    let grandTotal = subtotal;

    if (is_percentage === 1) {
      const discountAmount = (subtotal * parsedDiscount) / 100;
      grandTotal = subtotal - discountAmount;
    } else {
      grandTotal = subtotal - parsedDiscount;
    }

    return grandTotal > 0 ? grandTotal.toFixed(2) : "0.00";
  };

  const generateInvoiceId = () => {
    const timestamp = Date.now().toString().slice(-4);
    const randomNum = Math.floor(100 + Math.random() * 900);
    return `INV-${timestamp}`;
  };

  // Fetch functions
  const fetchProperties = async () => {
    try {
      const data = await projectAPI.getDropdowns();
      setProperties(Array.isArray(data.data) ? data.data : []);
    } catch {
      setProperties([]);
    }
  };

  const fetchUnits = async (propertyId) => {
    setUnitLoading(true);
    try {
      if (propertyId) {
        const data = await projectAPI.unitDropdown(propertyId);
        setUnits(Array.isArray(data.data) ? data.data : []);
      } else {
        setUnits([]);
      }
    } catch {
      setUnits([]);
    } finally {
      setUnitLoading(false);
    }
  };

  const fetchPaymentTypes = async () => {
    try {
      const data = await paymentTypeAPI.getDropdown();
      setPaymentTypes(data || []);
    } catch (err) {
      console.error("Failed to fetch payment types:", err);
    }
  };

  const fetchPaymentStatuses = async () => {
    try {
      const data = await paymentStatusAPI.getDropdown();
      setPaymentStatuses(data || []);
    } catch (err) {
      console.error("Failed to fetch payment statuses:", err);
    }
  };

  const fetchPaymentMethods = async () => {
    try {
      const data = await paymentMethodAPI.getDropdown();
      setPaymentMethods(data || []);
    } catch (err) {
      console.error("Failed to fetch payment methods:", err);
    }
  };

  const fetchTenants = async () => {
    try {
      const data = await tenantsAPI.getDropdown();
      setTenants(Array.isArray(data.data) ? data.data : []);
    } catch {
      setTenants([]);
    }
  };

  const fetchCustomers = async () => {
    try {
      const token = localStorage.getItem("auth_token");
      const res = await fetch("/api/customers", {
        headers: {
          Authorization: token ? `Bearer ${token}` : "",
          "Content-Type": "application/json",
          Accept: "application/json"
        }
      });
      const data = await res.json();
      setCustomers(Array.isArray(data.data) ? data.data : Array.isArray(data) ? data : []);
    } catch {
      setCustomers([]);
    }
  };

  const fetchCustomerById = async (customerId) => {
    try {
      const data = await customersAPI.getById(customerId);
      return data;
    } catch (err) {
      console.error("Failed to fetch customer:", err);
      return null;
    }
  };

  const fetchPropertyServices = async () => {
    try {
      const data = await propertyServiceAPI.getDropdown();
      setPropertyServices(data || []);
    } catch (err) {
      console.error("Failed to fetch property services:", err);
    }
  };

  const fetchInvoices = async () => {
    setLoading(true);
    try {
      const data = await invoiceAPI.getAll();
      let invoicesData;
      if (data.success && data.data && data.data.data) {
        invoicesData = data.data.data;
      } else {
        invoicesData = data.data || data;
      }
      setInvoices(invoicesData);
    } catch (err) {
      setError(err.message || "Failed to fetch invoices");
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchInvoices();
    fetchTenants();
    fetchPaymentMethods();
    fetchPaymentStatuses();
    fetchPaymentTypes();
    fetchPropertyServices();
    fetchProperties();
  }, []);

  // Auto-calculate total_amount when amount or tax_amount changes
  useEffect(() => {
    const base = parseFloat(form.amount) || 0;
    let tax = parseFloat(form.tax_amount) || 0;
    let total = base;
    if (tax > 0) {
      if (tax <= 100) {
        total = base + (base * tax / 100);
      } else {
        total = base + tax;
      }
    }
    if (form.total_amount !== (isNaN(total) ? '' : total.toFixed(2))) {
      setForm((prev) => ({ ...prev, total_amount: isNaN(total) ? '' : total.toFixed(2) }));
    }
  }, [form.amount, form.tax_amount]);

  // Calculate grand total
  const propertySubtotal = propertyItems.reduce((acc, item) => {
    const total = parseFloat(item.total_amount) || 0;
    return acc + total;
  }, 0);

  const itemsSubtotal = items.reduce((acc, item) => {
    const total = parseFloat(item.item_total_price) || 0;
    return acc + total;
  }, 0);

  const subtotal = propertySubtotal + itemsSubtotal;

  useEffect(() => {
    const newGrandTotal = calculateGrandTotal(subtotal, form.discount, form.is_percentage);
    setForm(prev => ({ ...prev, grand_total: newGrandTotal }));
  }, [subtotal, form.discount, form.is_percentage]);

  // Auto-calculate due_date
  useEffect(() => {
    if (form.total_amount>0){
      const total_amount = parseFloat(form.total_amount);
      const payment_amount = parseFloat(form.payment_amount);
      if (payment_amount < total_amount){
        const due_amount = total_amount - payment_amount;
        setForm((prev) => ({ ...prev, due_amount: due_amount.toFixed(2) }));
      }
      if (payment_amount >= total_amount){
          const due_amount = total_amount - payment_amount;
        setForm((prev) => ({ ...prev, due_amount: due_amount.toFixed(2) }));
      }
    }
  }, [form.payment_amount, form.total_amount]);
  
  // Fetch Statistics
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await invoiceAPI.getStatistics()
        const apiData = response.data || response

        setStatistics({
          counts: {
            total: apiData.counts?.total || 0,
            paid: apiData.counts?.paid || 0,
            pending: apiData.counts?.pending || apiData.counts?.unpaid || 0,
            overdue: apiData.counts?.overdue || 0,
            failed: apiData.counts?.failed || 0,
            refunded: apiData.counts?.refunded || 0,
            cancelled: apiData.counts?.cancelled || 0,
          },
          amounts: {
            total: apiData.amounts?.total || 0,
            paid: apiData.amounts?.paid || 0,
            pending: apiData.amounts?.pending || apiData.amounts?.unpaid || 0,
            overdue: apiData.amounts?.overdue || 0,
            failed: apiData.amounts?.failed || 0,
            refunded: apiData.amounts?.refunded || 0,
            cancelled: apiData.amounts?.cancelled || 0,
          }
        })
      } catch (error) {
        console.error("Failed to fetch invoice statistics:", error)
        setStatistics({
          counts: {
            total: 0,
            paid: 0,
            pending: 0,
            overdue: 0,
            failed: 0,
            refunded: 0,
            cancelled: 0,
          },
          amounts: {
            total: 0,
            paid: 0,
            pending: 0,
            overdue: 0,
            failed: 0,
            refunded: 0,
            cancelled: 0,
          }
        })
      } finally {
        setLoading(false)
      }
    }
    fetchStats()
  }, [])

  // Fetch units when property changes
  useEffect(() => {
    fetchUnits(form.property_id);
    setForm((prev) => ({ ...prev, unit_id: "" }));
  }, [form.property_id]);

  // Fetch customers when modal opens and invoice_type is not rent ('1')
  useEffect(() => {
    if (modalOpen && form.invoice_type && form.invoice_type !== "1") {
      fetchCustomers();
    }
  }, [modalOpen, form.invoice_type]);

  // Auto-fill amount logic
  useEffect(() => {
    if (!modalOpen || !form.property_id || unitLoading) return;

    if (units.length === 0) {
      const selectedProperty = properties.find((p) => p.id.toString() === form.property_id?.toString());
      if (selectedProperty && selectedProperty.total_price != null) {
        setForm((prev) => ({ ...prev, amount: selectedProperty.total_price.toString() }));
      } else {
        setForm((prev) => ({ ...prev, amount: '' }));
      }
    }
  }, [modalOpen, form.property_id, units, unitLoading, properties]);

  useEffect(() => {
    if (!modalOpen || !form.property_id || !form.unit_id || !form.invoice_type) return;

    const selectedUnit = units.find((u) => u.id.toString() === form.unit_id?.toString());
    if (selectedUnit) {
      let price = 0;
      if (form.invoice_type === '1' && selectedUnit.rent_price != null) {
        price = selectedUnit.rent_price;
      } else if (form.invoice_type === '2' && selectedUnit.lease_price != null) {
        price = selectedUnit.lease_price;
      } else if (form.invoice_type === '3' && selectedUnit.sell_price != null) {
        price = selectedUnit.sell_price;
      } else {
        price = selectedUnit.sell_price ?? selectedUnit.rent_price ?? selectedUnit.lease_price ?? 0;
      }
      setForm((prev) => ({ ...prev, amount: price.toString() }));
    }
  }, [modalOpen, form.property_id, form.unit_id, form.invoice_type, units]);

  // CRUD Handlers
  const handleCreate = () => {
    setEditInvoice(null);
    setForm({
      invoice_type: "2",
      invoice_number: generateInvoiceId(),
      invoice_date: "",
      customer_id: "",
      tenant_id: "",
      amount: "",
      grand_total: "",
      discount:"",
      is_percentage: 0,
      terms_and_conditions: "",
      tax_amount: "",
      total_amount: "", 
      payment_method_id: "",
      payment_Type_id: "",
      payment_amount: "",
      due_amount: "",
      payment_status_id: "",
      due_date: "",
      notes: "",
    });
    setPropertyItems([
      {
        property_id: "",
        unit_id: "",
        amount: "",
        tax_amount: "",
        total_amount: "",
      },
    ]);
    setItems([
      { item_name: "", qty: 1, item_price: 0, item_tax: 0, item_total_price: 0 }
    ]);
    setModalOpen(true);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const formData = { ...form, items, properties: propertyItems };
      if (editInvoice) {
        await invoiceAPI.update(editInvoice.id, formData);
      } else {
        await invoiceAPI.create(formData);
      }
      setModalOpen(false);
      setEditInvoice(null);
      fetchInvoices();
    } catch (err) {
      setError(err.message || "Failed to save invoice");
    } finally {
      setSaving(false);
    }
  };

  const handleEdit = (invoice) => {
    setEditInvoice(invoice);
    setForm({
      invoice_number: invoice.invoice_number || "",
      invoice_type: invoice.invoice_type ? invoice.invoice_type.toString() : "",
      tenant_id: invoice.tenant_id ? invoice.tenant_id.toString() : "",
      property_id: invoice.property_id ? invoice.property_id.toString() : "",
      unit_id: invoice.unit_id ? invoice.unit_id.toString() : "",
      customer_id: invoice.customer_id ? invoice.customer_id.toString() : "",
      invoice_date: formatDate(invoice.invoice_date),  
      due_date: formatDate(invoice.due_date),    
      terms_and_conditions: invoice.terms_and_conditions || "",
      billing_date: invoice.billing_date || "",
      billing_end: invoice.billing_end || "",
      amount: invoice.amount || "",
      tax_amount: invoice.tax_amount || "",
      discount_amount: invoice.discount_amount || "",
      discount: invoice.discount || "",
      total_amount: invoice.total_amount || "",
      grand_total: invoice.grand_total || "",
      is_percentage: invoice.is_percentage || 0,
      payment_status_id: invoice.payment_status_id ? invoice.payment_status_id.toString() : "",
      payment_method_id: invoice.payment_method_id ? invoice.payment_method_id.toString() : "",
      payment_Type_id: invoice.payment_Type_id ? invoice.payment_Type_id.toString() : "",
      payment_date: invoice.payment_date || "",
      payment_method: invoice.payment_method || "",
      payment_amount: invoice.payment_amount || "",
      due_amount: invoice.due_amount || "",
      notes: invoice.notes || ""
    });

    if (invoice.property_items && invoice.property_items.length > 0) {
      const populatedPropertyItems = invoice.property_items.map(item => ({
        property_id: item.property_id ? item.property_id.toString() : "",
        unit_id: item.unit_id ? item.unit_id.toString() : "",
        amount: item.amount || "",
        tax_amount: item.tax_amount || "",
        total_amount: item.total_amount || "",
      }));
      setPropertyItems(populatedPropertyItems);

      populatedPropertyItems.forEach((item, index) => {
        if (item.property_id) {
          fetchUnitsForPropertyItem(item.property_id, index);
        }
      });
    } else {
      setPropertyItems([
        {
          property_id: "",
          unit_id: "",
          amount: "",
          tax_amount: "",
          total_amount: "",
        },
      ]);
    }

    if (invoice.items && invoice.items.length > 0) {
      const populatedItems = invoice.items.map(item => ({
        item_name: item.item_name || "",
        qty: item.qty || 1,
        item_price: item.item_price || 0,
        item_tax: item.item_tax || 0,
        item_total_price: item.item_total_price || 0,
      }));
      setItems(populatedItems);
    } else {
      setItems([
        { item_name: "", qty: 1, item_price: 0, item_tax: 0, item_total_price: 0 }
      ]);
    }

    setModalOpen(true);
  };

  const handleDelete = async (invoice) => {
    if (window.confirm(`Delete invoice ${invoice.invoice_number}?`)) {
      setSaving(true);
      try {
        await invoiceAPI.delete(invoice.id);
        fetchInvoices();
      } catch (err) {
        setError(err.message || "Failed to delete invoice");
      } finally {
        setSaving(false);
      }
    }
  };

  const handleView = (invoice) => {
    alert(
      `Viewing invoice ${invoice.invoice_number}\nTenant: ${
        invoice.tenant?.first_name || invoice.tenant?.name || "N/A"
      }\nAmount: $${invoice.total_amount}\nStatus: ${invoice.status}`
    );
  };

  const handleSearchChange = (e) => setSearchTerm(e.target.value);
  const handleStatusChange = (value) => setStatusFilter(value);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Invoices</h1>
          <p className="text-muted-foreground">Manage and track customer invoices</p>
        </div>
        <Button onClick={handleCreate} className="gap-2">
          <Plus className="h-4 w-4" />
          Create Invoice
        </Button>
      </div>

      {/* Statistics */}
      <InvoiceStatistics statistics={statistics} />

      {/* Error Message */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6 flex items-center gap-2 text-red-600">
            <span className="text-sm font-medium">Error:</span>
            <span className="text-sm">{error}</span>
            <Button variant="outline" size="sm" onClick={() => setError(null)} className="ml-auto">
              Dismiss
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <InvoiceFilters
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        onSearchChange={handleSearchChange}
        onStatusChange={handleStatusChange}
        paymentStatuses={paymentStatuses}
      />

      {/* Table */}
      <InvoiceTable
        invoices={invoices}
        loading={loading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        paymentStatuses={paymentStatuses}
      />

      {/* Form Modal */}
      <InvoiceForm
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        onSave={handleSave}
        editInvoice={editInvoice}
        saving={saving}
        form={form}
        setForm={setForm}
        properties={properties}
        tenants={tenants}
        customers={customers}
        paymentMethods={paymentMethods}
        paymentStatuses={paymentStatuses}
        paymentTypes={paymentTypes}
        propertyServices={propertyServices}
        propertyItems={propertyItems}
        setPropertyItems={setPropertyItems}
        items={items}
        setItems={setItems}
        units={units}
        unitLoading={unitLoading}
        propertyUnits={propertyUnits}
        fetchUnitsForPropertyItem={fetchUnitsForPropertyItem}
        updatePropertyItem={updatePropertyItem}
        addPropertyItem={addPropertyItem}
        removePropertyItem={removePropertyItem}
        updateItem={updateItem}
        addItem={addItem}
        removeItem={removeItem}
        customerSearch={customerSearch}
        setCustomerSearch={setCustomerSearch}
      />
    </div>
  );
}

export default InvoicePage;
