import axios from 'axios';

const API_URL = '/api/unit-types';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add request interceptor to include auth token 
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

const unitTypeAPI = {
    // get all unit types with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching unit types:', error);
            throw error.response?.data || error;
        }

    },
    // get unit type by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching unit type:', error);
            throw error.response?.data || error;
        }
    },
    // create new unit type
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data;
        } catch (error) {
            console.error('Error creating unit type:', error);
            throw error.response?.data || error;
        }
    },
    // update unit type
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data;
        } catch (error) {
            console.error('Error updating unit type:', error);
            throw error.response?.data || error;
        }
    },
    // delete unit type
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting unit type:', error);
            throw error.response?.data || error;
        }
    },
     // Toggle status
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data;
        } catch (error) {
            console.error('Error toggling unit type status:', error);
            throw error.response?.data || error;
        }
    },
    // Get dropdown options (active unit types only)
    getDropdown: async () => {
        try {
            const response = await api.get(`${API_URL}/dropdown`);
            return response.data;
        } catch (error) {
            console.error('Error fetching unit types dropdown:', error);
            throw error.response?.data || error;
        }
    }

}


export default unitTypeAPI;
