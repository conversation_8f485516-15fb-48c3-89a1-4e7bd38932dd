import axios from "axios";

const API_URL = '/api/payment-methods';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add JWT token to request headers if available
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Payment Method API functions
const paymentMethodAPI = {
    // Get all payment methods with pagination and filters
    getAll: async (params = {}) => {
        try {   
            const response = await api.get(API_URL, { params });
            // Handle paginated response
            if (response.data.success && response.data.data && response.data.data.data) {
                return response.data; // Return full response for pagination info
            }
            return response.data;
        } catch (error) {
            console.error('Error fetching payment methods:', error);
            throw error.response?.data || error;
        }
    },

    // Get payment method by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching payment method:', error);
            throw error.response?.data || error;
        }
    },

    // Create new payment method
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error creating payment method:', error);
            throw error.response?.data || error;
        }
    },

    // Update payment method
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error updating payment method:', error);
            throw error.response?.data || error;
        }
    },

    // Delete payment method
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error deleting payment method:', error);
            throw error.response?.data || error;
        }
    },

    // Toggle payment method status (active/inactive)
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error toggling payment method status:', error);
            throw error.response?.data || error;
        }
    },

    // Get dropdown options (active payment methods only)
    getDropdown: async () => {
        try {
            const response = await api.get(`${API_URL}/dropdown`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching payment methods dropdown:', error);
            throw error.response?.data || error;
        }
    }
}

export default paymentMethodAPI;
