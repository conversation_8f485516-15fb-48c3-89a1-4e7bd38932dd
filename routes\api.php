<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LandAcquisitionController;
use Modules\LandOwners\Http\Controllers\LandOwnerController;
use App\Http\Controllers\LandDocumentController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ProductionDiagnosticsController;
use App\Http\Controllers\ModuleAvailabilityController;
use App\Http\Controllers\CountryController;
use App\Http\Controllers\StateController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\UnitDetailController;
use App\Http\Controllers\ContractorController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\VendorTypeController;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\PropertyAmenityController;
use App\Http\Controllers\PropertyTypeController;
use App\Http\Controllers\PropertyStatusController;
// use App\Http\Controllers\ProjectContractorController;
// use App\Http\Controllers\ProjectVendorController;
// use App\Http\Controllers\ProjectEmployeeController;
use App\Http\Controllers\CustomersController;
use App\Http\Controllers\Api\UnitTypeController;
use App\Http\Controllers\PropertyStageController;
use App\Http\Controllers\TanantController;
use App\Http\Controllers\PropertyServicesController;
use App\Http\Controllers\RentTypeController;
use App\Http\Controllers\LeaseTypeController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\PaymentTypeController;
use App\Http\Controllers\PaymentStatusController;
use App\Http\Controllers\PaymentMethodController;


// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    
    // Protected authentication routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('logout-all', [AuthController::class, 'logoutAll']);
        Route::get('profile', [AuthController::class, 'profile']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
        Route::get('permissions', [AuthController::class, 'permissions']);
        Route::post('refresh-token', [AuthController::class, 'refreshToken']);
    });
});

// Public dropdown routes for form purposes
Route::get('vendor-types-dropdown', [VendorTypeController::class, 'dropdown']);
Route::get('countries-dropdown', [CountryController::class, 'dropdown']); // For country dropdown
Route::get('states-dropdown', [StateController::class, 'dropdown']); // For state dropdown  
Route::get('states/country/{countryId}', [StateController::class, 'byCountry']); // For states by country
Route::get('locations-dropdown', [LocationController::class, 'dropdown']); // For location dropdown
Route::get('locations/state/{stateId}', [LocationController::class, 'byState']); // For locations by state
Route::get('locations/country/{countryId}', [LocationController::class, 'byCountry']); // For locations by country
Route::get('property-amenities/active', [PropertyAmenityController::class, 'getActiveAmenities']); // For property amenities dropdown
Route::get('property-types/dropdown', [PropertyTypeController::class, 'dropdown']); // For property types dropdown
Route::get('unit-types/dropdown', [UnitTypeController::class, 'getDropdown']); // For unit types dropdown
Route::get('property-statuses/dropdown', [PropertyStatusController::class, 'dropdown']); // For property statuses dropdown
Route::get('property-stages/dropdown', [PropertyStageController::class, 'dropdown']); // For property stages dropdown
Route::get('projects-dropdown', [ProjectController::class, 'dropdown']);
Route::get('tanants/dropdown', [TanantController::class, 'dropdown']); // For tanants dropdown
Route::get('property-services/dropdown', [PropertyServicesController::class, 'dropDown']); // For property services dropdown
Route::get('rent-types/dropdown', [RentTypeController::class, 'dropdown']); // For rent types dropdown
Route::get('lease-types/dropdown', [LeaseTypeController::class, 'dropdown']); // For lease types dropdown
Route::get('invoices/dropdown', [InvoiceController::class, 'dropdown']); // For invoices dropdown
Route::get('unit-dropdown', [ProjectController::class, 'getDropdownUnits']); // For project units dropdown
Route::get('payment-types/dropdown', [PaymentTypeController::class, 'dropdown']); // For payment types dropdown
Route::get('payment-statuses/dropdown', [PaymentStatusController::class, 'dropdown']); // For payment statuses dropdown
Route::get('payment-methods/dropdown', [PaymentMethodController::class, 'dropdown']); // For payment methods dropdown
Route::get('invoices-statistics', [InvoiceController::class, 'getStatistics']);

// Public module availability routes
Route::get('modules/available', [ModuleAvailabilityController::class, 'getAvailableModules']);
Route::get('modules/sidebar-config', [ModuleAvailabilityController::class, 'getSidebarConfig']);
Route::get('modules/check/{moduleKey}', [ModuleAvailabilityController::class, 'checkModule']);
Route::get('module-availability', [ModuleAvailabilityController::class, 'getSidebarConfig']);

// Temporary: Land Owners routes for testing (REMOVE AUTHENTICATION FOR DEBUGGING)
Route::get('land-owners', [LandOwnerController::class, 'index']);
Route::get('land-owners-statistics', [LandOwnerController::class, 'statistics']);
Route::get('land-owners/dropdown', [LandOwnerController::class, 'dropdown']);
Route::get('land-owners/{landOwner}', [LandOwnerController::class, 'show']);
Route::post('land-owners', [LandOwnerController::class, 'store']);
Route::put('land-owners/{landOwner}', [LandOwnerController::class, 'update']);
Route::patch('land-owners/{landOwner}/status', [LandOwnerController::class, 'updateStatus']);
Route::delete('land-owners/{landOwner}', [LandOwnerController::class, 'destroy']);

// Temporary: Land Acquisitions routes for testing (REMOVE AUTHENTICATION FOR DEBUGGING)
Route::get('land-acquisitions', [LandAcquisitionController::class, 'index']);
Route::get('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'show']);
Route::get('land-acquisitions-statistics', [LandAcquisitionController::class, 'statistics']);
Route::post('land-acquisitions', [LandAcquisitionController::class, 'store']);
Route::put('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']);
Route::patch('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']);
Route::post('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']);
Route::delete('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'destroy']);

// Temporary: Land Documents routes for testing (REMOVE AUTHENTICATION FOR DEBUGGING)
Route::get('land-documents', [LandDocumentController::class, 'index']);
Route::get('land-documents/{landDocument}', [LandDocumentController::class, 'show']);
Route::get('land-documents/{landDocument}/download', [LandDocumentController::class, 'download']);
Route::post('land-documents', [LandDocumentController::class, 'store']);
Route::put('land-documents/{landDocument}', [LandDocumentController::class, 'update']);
Route::delete('land-documents/{landDocument}', [LandDocumentController::class, 'destroy']);

// Protected API routes - require authentication
Route::middleware('auth:sanctum')->group(function () {
    
Route::prefix('customers')->middleware('auth:sanctum')->group(function () {
    
    // Read (View Customers)
        Route::middleware('permission:customer,read')->group(function () {
            Route::get('/', [CustomersController::class, 'index']);
            Route::get('/{id}', [CustomersController::class, 'show']);
        });

        // Create
        Route::middleware('permission:customer,create')->post('/', [CustomersController::class, 'store']);

        // Update
        Route::middleware('permission:customer,update')->put('/{id}', [CustomersController::class, 'update']);

        //  Delete
        Route::middleware('permission:customer,delete')->delete('/{id}', [CustomersController::class, 'destroy']);
    });

    // rent type management -with permission checks
    Route::middleware('permission:rent-type,read')->group(function () {
        Route::get('rent-types', [RentTypeController::class, 'index']);
        Route::get('rent-types/{rentType}', [RentTypeController::class, 'show']);
        Route::get('rent-types-dropdown', [RentTypeController::class, 'dropDown']);
    });
    
    Route::middleware('permission:rent-type,create')->group(function () {
        Route::post('rent-types', [RentTypeController::class, 'store']);
    });
    
    Route::middleware('permission:rent-type,update')->group(function () {
        Route::put('rent-types/{rentType}', [RentTypeController::class, 'update']);
        Route::patch('rent-types/{rentType}', [RentTypeController::class, 'update']);
        Route::post('rent-types/{rentType}/toggle-status', [RentTypeController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:rent-type,delete')->group(function () {
        Route::delete('rent-types/{rentType}', [RentTypeController::class, 'destroy']);
    });
    
    // Role Management routes - with permission checks
    Route::middleware('permission:role,read')->group(function () {
        Route::get('roles', [RoleController::class, 'index']);
        Route::get('roles/{role}', [RoleController::class, 'show']);
        Route::get('roles-statistics', [RoleController::class, 'getStatistics']);
        Route::get('roles-modules', [RoleController::class, 'getAvailableModules']);
    });
    
    Route::middleware('permission:role,create')->group(function () {
        Route::post('roles', [RoleController::class, 'store']);
    });
    
    Route::middleware('permission:role,update')->group(function () {
        Route::put('roles/{role}', [RoleController::class, 'update']);
        Route::patch('roles/{role}', [RoleController::class, 'update']);
        Route::patch('roles-bulk-status', [RoleController::class, 'bulkUpdateStatus']);
    });
    
    Route::middleware('permission:role,delete')->group(function () {
        Route::delete('roles/{role}', [RoleController::class, 'destroy']);
    });

    // Country routes - with permission checks
    Route::middleware('permission:country,read')->group(function () {
        Route::get('countries', [CountryController::class, 'index']);
        Route::get('countries/{country}', [CountryController::class, 'show']);
        Route::get('countries-statistics', [CountryController::class, 'getStatistics']);
        Route::get('countries-continents', [CountryController::class, 'getContinents']);
    });
    
    Route::middleware('permission:country,create')->group(function () {
        Route::post('countries', [CountryController::class, 'store']);
    });
    
    Route::middleware('permission:country,update')->group(function () {
        Route::put('countries/{country}', [CountryController::class, 'update']);
        Route::patch('countries/{country}', [CountryController::class, 'update']);
        Route::patch('countries/{country}/toggle-status', [CountryController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:country,delete')->group(function () {
        Route::delete('countries/{country}', [CountryController::class, 'destroy']);
    });
    // Payment Types routes with permission
    Route::middleware('permission:payment-type,read')->group(function () {
        Route::get('payment-types', [PaymentTypeController::class, 'index']);
        Route::get('payment-types/{paymentType}', [PaymentTypeController::class, 'show']);
     
    });
    Route::middleware('permission:payment-type,create')->group(function () {
        Route::post('payment-types', [PaymentTypeController::class, 'store']);
    });
    Route::middleware('permission:payment-type,update')->group(function () {
        Route::put('payment-types/{paymentType}', [PaymentTypeController::class, 'update']);
        Route::patch('payment-types/{paymentType}', [PaymentTypeController::class, 'update']);
        Route::patch('payment-types/{paymentType}/toggle-status', [PaymentTypeController::class, 'toggleStatus']);
    });
    Route::middleware('permission:payment-type,delete')->group(function () {
        Route::delete('payment-types/{paymentType}', [PaymentTypeController::class, 'destroy']);
    });


    // payment method routes with permission
    Route::middleware('permission:payment-method,read')->group(function () {
        Route::get('payment-methods', [PaymentMethodController::class, 'index']);
        Route::get('payment-methods/{paymentMethod}', [PaymentMethodController::class, 'show']);
        
    });
    Route::middleware('permission:payment-method,create')->group(function () {
        Route::post('payment-methods', [PaymentMethodController::class, 'store']);
    });
    Route::middleware('permission:payment-method,update')->group(function () {
        Route::put('payment-methods/{paymentMethod}', [PaymentMethodController::class, 'update']);
        Route::patch('payment-methods/{paymentMethod}', [PaymentMethodController::class, 'update']);
        Route::patch('payment-methods/{paymentMethod}/toggle-status', [PaymentMethodController::class, 'toggleStatus']);
    });
    Route::middleware('permission:payment-method,delete')->group(function () {
        Route::delete('payment-methods/{paymentMethod}', [PaymentMethodController::class, 'destroy']);
    });


    // payment status routes with permission

    Route::middleware('permission:payment-status,read')->group(function () {
        Route::get('payment-statuses', [PaymentStatusController::class, 'index']);
        Route::get('payment-statuses/{paymentStatus}', [PaymentStatusController::class, 'show']);
        Route::get('payment-statuses-dropdown', [PaymentStatusController::class, 'dropdown']);
    });
    Route::middleware('permission:payment-status,create')->group(function () {
        Route::post('payment-statuses', [PaymentStatusController::class, 'store']);
    });
    Route::middleware('permission:payment-status,update')->group(function () {
        Route::put('payment-statuses/{paymentStatus}', [PaymentStatusController::class, 'update']);
        Route::patch('payment-statuses/{paymentStatus}', [PaymentStatusController::class, 'update']);
        Route::patch('payment-statuses/{paymentStatus}/toggle-status', [PaymentStatusController::class, 'toggleStatus']);
    });
    Route::middleware('permission:payment-status,delete')->group(function () {
        Route::delete('payment-statuses/{paymentStatus}', [PaymentStatusController::class, 'destroy']);
    });

 
    // invoice routes with permission
    Route::middleware('permission:invoice,read')->group(function () {
        Route::get('invoices', [InvoiceController::class, 'index']);
        Route::get('invoices/{invoice}', [InvoiceController::class, 'show']);
        
    });
    
    Route::middleware('permission:invoice,create')->group(function () {
        Route::post('invoices', [InvoiceController::class, 'store']);
    });
    
    Route::middleware('permission:invoice,update')->group(function () {
        Route::put('invoices/{invoice}', [InvoiceController::class, 'update']);
        Route::patch('invoices/{invoice}', [InvoiceController::class, 'update']);
        Route::patch('invoices/{invoice}/toggle-status', [InvoiceController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:invoice,delete')->group(function () {
        Route::delete('invoices/{invoice}', [InvoiceController::class, 'destroy']);
    });

    
    // Lease type routes with Permission
    Route::middleware('permission:lease-type,read')->group(function () {
        Route::get('lease-types', [LeaseTypeController::class, 'index']);
        Route::get('lease-types/{leaseType}', [LeaseTypeController::class, 'show']);
        Route::get('lease-types-dropdown', [LeaseTypeController::class, 'dropdown']);
    });
    
    Route::middleware('permission:lease-type,create')->group(function () {
        Route::post('lease-types', [LeaseTypeController::class, 'store']);
    });
    
    Route::middleware('permission:lease-type,update')->group(function () {
        Route::put('lease-types/{leaseType}', [LeaseTypeController::class, 'update']);
        Route::patch('lease-types/{leaseType}', [LeaseTypeController::class, 'update']);
        Route::patch('lease-types/{leaseType}/toggle-status', [LeaseTypeController::class, 'toggleStatus']);
    });

    Route::middleware('permission:lease-type,delete')->group(function () {
        Route::delete('lease-types/{leaseType}', [LeaseTypeController::class, 'destroy']);
    });
    
    // State routes - with permission checks
    Route::middleware('permission:state,read')->group(function () {
        Route::get('states', [StateController::class, 'index']);
        Route::get('states/{state}', [StateController::class, 'show']);
        Route::get('states-statistics', [StateController::class, 'getStatistics']);
        Route::get('states/country/{countryId}', [StateController::class, 'byCountry']);
    });
    
    Route::middleware('permission:state,create')->group(function () {
        Route::post('states', [StateController::class, 'store']);
    });
    
    Route::middleware('permission:state,update')->group(function () {
        Route::put('states/{state}', [StateController::class, 'update']);
        Route::patch('states/{state}', [StateController::class, 'update']);
        Route::patch('states/{state}/toggle-status', [StateController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:state,delete')->group(function () {
        Route::delete('states/{state}', [StateController::class, 'destroy']);
    });

    // Property Services routes - with permission checks
    Route::middleware('permission:property-service,read')->group(function () {
        Route::get('property-services', [PropertyServicesController::class, 'index']);
        Route::get('property-services/{propertyService}', [PropertyServicesController::class, 'show']);
        Route::get('property-services-dropdown', [PropertyServicesController::class, 'dropDown']);
    });
    
    Route::middleware('permission:property-service,create')->group(function () {
        Route::post('property-services', [PropertyServicesController::class, 'store']);
    });
    
    Route::middleware('permission:property-service,update')->group(function () {
        Route::put('property-services/{propertyService}', [PropertyServicesController::class, 'update']);
        Route::patch('property-services/{propertyService}', [PropertyServicesController::class, 'update']);
        Route::patch('property-services/{propertyService}/toggle-status', [PropertyServicesController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:property-service,delete')->group(function () {
        Route::delete('property-services/{propertyService}', [PropertyServicesController::class, 'destroy']);
    }); 

    // Location routes - with permission checks
    Route::middleware('permission:location,read')->group(function () {
        Route::get('locations', [LocationController::class, 'index']);
        Route::get('locations/{location}', [LocationController::class, 'show']);
        Route::get('locations-statistics', [LocationController::class, 'statistics']);
        // Note: locations/state/{stateId} and locations/country/{countryId} are public routes above
    });
    
    Route::middleware('permission:location,create')->group(function () {
        Route::post('locations', [LocationController::class, 'store']);
    });
    
    Route::middleware('permission:location,update')->group(function () {
        Route::put('locations/{location}', [LocationController::class, 'update']);
        Route::patch('locations/{location}', [LocationController::class, 'update']);
        Route::patch('locations/{location}/toggle-status', [LocationController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:location,delete')->group(function () {
        Route::delete('locations/{location}', [LocationController::class, 'destroy']);
    });

    // Language routes - with permission checks
    Route::middleware('permission:language,read')->group(function () {
        Route::get('languages', [LanguageController::class, 'index']);
        Route::get('languages/{language}', [LanguageController::class, 'show']);
        Route::get('languages-statistics', [LanguageController::class, 'getStatistics']);
    });
    
    Route::middleware('permission:language,create')->group(function () {
        Route::post('languages', [LanguageController::class, 'store']);
    });
    
    Route::middleware('permission:language,update')->group(function () {
        Route::put('languages/{language}', [LanguageController::class, 'update']);
        Route::patch('languages/{language}', [LanguageController::class, 'update']);
        Route::patch('languages/{language}/set-default', [LanguageController::class, 'setDefault']);
    });
    
    Route::middleware('permission:language,delete')->group(function () {
        Route::delete('languages/{language}', [LanguageController::class, 'destroy']);
    });

    // Currency routes - with permission checks
    Route::middleware('permission:currency,read')->group(function () {
        Route::get('currencies', [CurrencyController::class, 'index']);
        Route::get('currencies/{currency}', [CurrencyController::class, 'show']);
        Route::get('currencies-statistics', [CurrencyController::class, 'getStatistics']);
    });
    
    Route::middleware('permission:currency,create')->group(function () {
        Route::post('currencies', [CurrencyController::class, 'store']);
    });
    
    Route::middleware('permission:currency,update')->group(function () {
        Route::put('currencies/{currency}', [CurrencyController::class, 'update']);
        Route::patch('currencies/{currency}', [CurrencyController::class, 'update']);
        Route::patch('currencies/{currency}/toggle-status', [CurrencyController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:currency,delete')->group(function () {
        Route::delete('currencies/{currency}', [CurrencyController::class, 'destroy']);
    });

    // Project routes - with permission checks
    Route::middleware('permission:project,read')->group(function () {
        Route::get('projects', [ProjectController::class, 'index']);
        Route::get('projects/{project}', [ProjectController::class, 'show']);
        Route::get('projects-statistics', [ProjectController::class, 'statistics']);
      
    });
    
    Route::middleware('permission:project,create')->group(function () {
        Route::post('projects', [ProjectController::class, 'store']);
    });
    
    Route::middleware('permission:project,update')->group(function () {
        Route::put('projects/{project}', [ProjectController::class, 'update']);
        Route::patch('projects/{project}', [ProjectController::class, 'update']);
        Route::post('projects/{project}', [ProjectController::class, 'update']); // For file uploads with _method=PUT
        Route::delete('projects/{project}/images/{imageId}', [ProjectController::class, 'deleteImage']); // Hard delete individual images
    });
    
    Route::middleware('permission:project,delete')->group(function () {
        Route::delete('projects/{project}', [ProjectController::class, 'destroy']);
    });

    // Unit Detail routes - with permission checks

    // Unit Details routes - all require authentication, permissions will be added as needed
    Route::get('unit-details', [UnitDetailController::class, 'index']);
    Route::get('unit-details/{unitDetail}', [UnitDetailController::class, 'show']);
    Route::post('unit-details', [UnitDetailController::class, 'store']);
    Route::put('unit-details/{unitDetail}', [UnitDetailController::class, 'update']);
    Route::patch('unit-details/{unitDetail}', [UnitDetailController::class, 'update']);
    Route::delete('unit-details/{unitDetail}', [UnitDetailController::class, 'destroy']);

   

    // Employee routes - with permission checks
    Route::get('employees', [EmployeeController::class, 'index']);
    Route::get('employees-dropdown', [EmployeeController::class, 'dropdown']);
    Route::get('employees-statistics', [EmployeeController::class, 'statistics']);
    Route::get('employees/{employee}', [EmployeeController::class, 'show']);
    Route::post('employees', [EmployeeController::class, 'store']);
    Route::put('employees/{employee}', [EmployeeController::class, 'update']);
    Route::patch('employees/{employee}', [EmployeeController::class, 'update']);
    Route::delete('employees/{employee}', [EmployeeController::class, 'destroy']);
    Route::post('employees/bulk-status-update', [EmployeeController::class, 'bulkStatusUpdate']);
    Route::put('employees/{employee}', [EmployeeController::class, 'update']);
    Route::patch('employees/{employee}', [EmployeeController::class, 'update']);
    Route::delete('employees/{employee}', [EmployeeController::class, 'destroy']);
    Route::post('employees/bulk-status-update', [EmployeeController::class, 'bulkStatusUpdate']);

    // Contractor routes - with permission checks
    Route::get('contractors', [ContractorController::class, 'index']);
    Route::get('contractors-dropdown', [ContractorController::class, 'dropdown']);
    Route::get('contractors-statistics', [ContractorController::class, 'statistics']);
    Route::get('contractors/{contractor}', [ContractorController::class, 'show']);
    Route::post('contractors', [ContractorController::class, 'store']);
    Route::put('contractors/{contractor}', [ContractorController::class, 'update']);
    Route::patch('contractors/{contractor}', [ContractorController::class, 'update']);
    Route::delete('contractors/{contractor}', [ContractorController::class, 'destroy']);
    Route::post('contractors/bulk-status-update', [ContractorController::class, 'bulkStatusUpdate']);

    // Project Contractor Assignment routes - Assign Contractor module
    // TODO: Uncomment when ProjectContractorController is created
    /*
    Route::middleware('permission:assign-contractor,read')->group(function () {
        Route::get('project-contractors', [ProjectContractorController::class, 'index']);
        Route::get('project-contractors-statistics', [ProjectContractorController::class, 'statistics']);
        Route::get('project-contractors-projects', [ProjectContractorController::class, 'getProjects']);
        Route::get('project-contractors-contractors', [ProjectContractorController::class, 'getContractors']);
        Route::get('project-contractors/{projectContractor}', [ProjectContractorController::class, 'show']);
    });
    
    Route::middleware('permission:assign-contractor,create')->group(function () {
        Route::post('project-contractors', [ProjectContractorController::class, 'store']);
    });
    
    Route::middleware('permission:assign-contractor,update')->group(function () {
        Route::put('project-contractors/{projectContractor}', [ProjectContractorController::class, 'update']);
    });
    
    Route::middleware('permission:assign-contractor,delete')->group(function () {
        Route::delete('project-contractors/{projectContractor}', [ProjectContractorController::class, 'destroy']);
    });

    // Project Vendor Assignment routes - Assign Vendor module
    Route::middleware('permission:assign-vendor,read')->group(function () {
        Route::get('project-vendors', [ProjectVendorController::class, 'index']);
        Route::get('project-vendors-statistics', [ProjectVendorController::class, 'statistics']);
        Route::get('project-vendors-projects', [ProjectVendorController::class, 'getProjects']);
        Route::get('project-vendors-vendors', [ProjectVendorController::class, 'getVendors']);
        Route::get('project-vendors/{projectVendor}', [ProjectVendorController::class, 'show']);
    });
    
    Route::middleware('permission:assign-vendor,create')->group(function () {
        Route::post('project-vendors', [ProjectVendorController::class, 'store']);
    });
    
    Route::middleware('permission:assign-vendor,update')->group(function () {
        Route::put('project-vendors/{projectVendor}', [ProjectVendorController::class, 'update']);
    });
    
    Route::middleware('permission:assign-vendor,delete')->group(function () {
        Route::delete('project-vendors/{projectVendor}', [ProjectVendorController::class, 'destroy']);
    });

    // Project Employee Assignment routes - Assign Employee module
    Route::middleware('permission:assign-employee,read')->group(function () {
        Route::get('project-employees', [ProjectEmployeeController::class, 'index']);
        Route::get('project-employees-statistics', [ProjectEmployeeController::class, 'statistics']);
        Route::get('project-employees-projects', [ProjectEmployeeController::class, 'getProjects']);
        Route::get('project-employees-employees', [ProjectEmployeeController::class, 'getEmployees']);
        Route::get('project-employees/{projectEmployee}', [ProjectEmployeeController::class, 'show']);
    });
    
    Route::middleware('permission:assign-employee,create')->group(function () {
        Route::post('project-employees', [ProjectEmployeeController::class, 'store']);
    });
    
    Route::middleware('permission:assign-employee,update')->group(function () {
        Route::put('project-employees/{projectEmployee}', [ProjectEmployeeController::class, 'update']);
    });
    
    Route::middleware('permission:assign-employee,delete')->group(function () {
        Route::delete('project-employees/{projectEmployee}', [ProjectEmployeeController::class, 'destroy']);
    });
    */

    // Vendor Type routes - with permission checks
    Route::middleware('permission:vendor-type,read')->group(function () {
        Route::get('vendor-types', [VendorTypeController::class, 'index']);
        Route::get('vendor-types/{vendorType}', [VendorTypeController::class, 'show']);
        Route::get('vendor-types-statistics', [VendorTypeController::class, 'statistics']);
    });
    
    Route::middleware('permission:vendor-type,create')->group(function () {
        Route::post('vendor-types', [VendorTypeController::class, 'store']);
    });
    
    Route::middleware('permission:vendor-type,update')->group(function () {
        Route::put('vendor-types/{vendorType}', [VendorTypeController::class, 'update']);
        Route::patch('vendor-types/{vendorType}', [VendorTypeController::class, 'update']);
        Route::post('vendor-types/bulk-status-update', [VendorTypeController::class, 'bulkStatusUpdate']);
    });
    
    Route::middleware('permission:vendor-type,delete')->group(function () {
        Route::delete('vendor-types/{vendorType}', [VendorTypeController::class, 'destroy']);
    });

    // Vendor routes - with permission checks
    Route::middleware('permission:vendor,read')->group(function () {
        Route::get('vendors', [VendorController::class, 'index']);
        Route::get('vendors/{vendor}', [VendorController::class, 'show']);
        Route::get('vendors-dropdown', [VendorController::class, 'dropdown']);
        Route::get('vendors-statistics', [VendorController::class, 'statistics']);
    });
    
    Route::middleware('permission:vendor,create')->group(function () {
        Route::post('vendors', [VendorController::class, 'store']);
    });
    
    Route::middleware('permission:vendor,update')->group(function () {
        Route::put('vendors/{vendor}', [VendorController::class, 'update']);
        Route::patch('vendors/{vendor}', [VendorController::class, 'update']);
        Route::post('vendors/bulk-status-update', [VendorController::class, 'bulkStatusUpdate']);
    });
    
    Route::middleware('permission:vendor,delete')->group(function () {
        Route::delete('vendors/{vendor}', [VendorController::class, 'destroy']);
    });

    // Property Stages routes - with permission checks
    Route::middleware('permission:property-stage,read')->group(function () {
        Route::get('property-stages', [PropertyStageController::class, 'index']);
        Route::get('property-stages/{propertyStage}', [PropertyStageController::class, 'show']);
        Route::get('property-stages-statistics', [PropertyStageController::class, 'statistics']);
    });
    
    Route::middleware('permission:property-stage,create')->group(function () {
        Route::post('property-stages', [PropertyStageController::class, 'store']);
    });
    
    Route::middleware('permission:property-stage,update')->group(function () {
        Route::put('property-stages/{propertyStage}', [PropertyStageController::class, 'update']);
        Route::patch('property-stages/{propertyStage}', [PropertyStageController::class, 'update']);
        Route::patch('property-stages/{propertyStage}/toggle-status', [PropertyStageController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:property-stage,delete')->group(function () {
        Route::delete('property-stages/{propertyStage}', [PropertyStageController::class, 'destroy']);
    });


    // Tanant routes - with permission checks
    Route::middleware('permission:tanant,read')->group(function () {
        Route::get('tanants', [TanantController::class, 'index']);
        Route::get('tanants/{tanant}', [TanantController::class, 'show']);
        Route::get('tanants-statistics', [TanantController::class, 'getStatistics']);
    });
    
    Route::middleware('permission:tanant,create')->group(function () {
        Route::post('tanants', [TanantController::class, 'store']);
    });
    
    Route::middleware('permission:tanant,update')->group(function () {
        Route::put('tanants/{tanant}', [TanantController::class, 'update']);
        Route::patch('tanants/{tanant}', [TanantController::class, 'update']);
        Route::post('tanants/bulk-status-update', [TanantController::class, 'bulkStatusUpdate']);
    });
    
    Route::middleware('permission:tanant,delete')->group(function () {
        Route::delete('tanants/{tanant}', [TanantController::class, 'destroy']);
    }); 


    // unit types routes -with permission checks
    Route::middleware('permission:unit-type,read')->group(function () {
        Route::get('unit-types', [UnitTypeController::class, 'index']);
        Route::get('unit-types/{unitType}', [UnitTypeController::class, 'show']);
    });
    
    Route::middleware('permission:unit-type,create')->group(function () {
        Route::post('unit-types', [UnitTypeController::class, 'store']);
    });
    
    Route::middleware('permission:unit-type,update')->group(function () {
        Route::put('unit-types/{unitType}', [UnitTypeController::class, 'update']);
        Route::patch('unit-types/{unitType}', [UnitTypeController::class, 'update']);
        Route::patch('unit-types/{unitType}/toggle-status', [UnitTypeController::class, 'toggleStatus']);
        Route::post('unit-types/bulk-status-update', [UnitTypeController::class, 'bulkStatusUpdate']);
        Route::post('unit-types/reorder', [UnitTypeController::class, 'reorder']);
    });
    
    Route::middleware('permission:unit-type,delete')->group(function () {
        Route::delete('unit-types/{unitType}', [UnitTypeController::class, 'destroy']);
    });

    // Property Types routes - with permission checks
    Route::middleware('permission:property-type,read')->group(function () {
        Route::get('property-types', [PropertyTypeController::class, 'index']);
        Route::get('property-types/{propertyType}', [PropertyTypeController::class, 'show']);
    });
    
    Route::middleware('permission:property-type,create')->group(function () {
        Route::post('property-types', [PropertyTypeController::class, 'store']);
    });
    
    Route::middleware('permission:property-type,update')->group(function () {
        Route::put('property-types/{propertyType}', [PropertyTypeController::class, 'update']);
        Route::patch('property-types/{propertyType}', [PropertyTypeController::class, 'update']);
        Route::patch('property-types/{propertyType}/toggle-status', [PropertyTypeController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:property-type,delete')->group(function () {
        Route::delete('property-types/{propertyType}', [PropertyTypeController::class, 'destroy']);
    });

    // Property Status routes - with permission checks
    Route::middleware('permission:property-status,read')->group(function () {
        Route::get('property-statuses', [PropertyStatusController::class, 'index']);
        Route::get('property-statuses/{propertyStatus}', [PropertyStatusController::class, 'show']);
    });
    
    Route::middleware('permission:property-status,create')->group(function () {
        Route::post('property-statuses', [PropertyStatusController::class, 'store']);
    });
    
    Route::middleware('permission:property-status,update')->group(function () {
        Route::put('property-statuses/{propertyStatus}', [PropertyStatusController::class, 'update']);
        Route::patch('property-statuses/{propertyStatus}', [PropertyStatusController::class, 'update']);
        Route::patch('property-statuses/{propertyStatus}/toggle-status', [PropertyStatusController::class, 'toggleStatus']);
        Route::patch('property-statuses/sort-orders', [PropertyStatusController::class, 'updateSortOrders']);
    });
    
    Route::middleware('permission:property-status,delete')->group(function () {
        Route::delete('property-statuses/{propertyStatus}', [PropertyStatusController::class, 'destroy']);
    });

    // Property Amenities routes - with permission checks
    Route::middleware('permission:property-amenity,read')->group(function () {
        Route::get('property-amenities', [PropertyAmenityController::class, 'index']);
        Route::get('property-amenities/{amenity}', [PropertyAmenityController::class, 'show']);
    });
    
    Route::middleware('permission:property-amenity,create')->group(function () {
        Route::post('property-amenities', [PropertyAmenityController::class, 'store']);
    });
    
    Route::middleware('permission:property-amenity,update')->group(function () {
        Route::put('property-amenities/{amenity}', [PropertyAmenityController::class, 'update']);
        Route::patch('property-amenities/{amenity}', [PropertyAmenityController::class, 'update']);
        Route::patch('property-amenities/bulk-status', [PropertyAmenityController::class, 'bulkUpdateStatus']);
        Route::post('property-amenities/reorder', [PropertyAmenityController::class, 'reorder']);
    });
    
    Route::middleware('permission:property-amenity,delete')->group(function () {
        Route::delete('property-amenities/{amenity}', [PropertyAmenityController::class, 'destroy']);
    });
});

// Environment-aware API info endpoint (public)
Route::get('info', function() {
    return response()->json([
        'success' => true,
        'environment' => app()->environment(),
        'app_url' => config('app.url'),
        'api_base_url' => config('app.url') . '/api',
        'request_info' => [
            'host' => request()->getHost(),
            'port' => request()->getPort(),
            'scheme' => request()->getScheme(),
            'full_url' => request()->fullUrl(),
        ],
        'cors_origins' => explode(',', env('CORS_ALLOWED_ORIGINS', '')),
        'sanctum_domains' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', '')),
    ]);
});
