<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LeaseType extends Model
{
    //
    protected $fillable = [
        'name',
        'description',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    protected $attributes = [
        'is_active' => true,
        'sort_order' => 0
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
