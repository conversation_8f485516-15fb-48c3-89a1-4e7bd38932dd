import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { MoreHorizontal, Edit2, Trash2, Power, Plus, X } from "lucide-react";
import manageTypesAPI from "../../services/manageTypesAPI";

export default function ManageTypesTable() {
  const [allTypes, setAllTypes] = useState({});
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  
  const itemsPerPage = 15;
  const [selectedItem, setSelectedItem] = useState({
    name: "",
    description: "",
    type: ""
  });
  const fetchTypes = async () => {
    try {
      setLoading(true);
      const response = await manageTypesAPI.getAll();
      setAllTypes(response.data || {});
    } catch (error) {
      console.error("Error fetching types:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTypes();
  }, []);

  const config = {
    propertyTypes: { label: "Property Type", activeKey: "status" },
    propertyStatuses: { label: "Property Status", activeKey: "status" },
    propertyStages: { label: "Property Condition", activeKey: "status" },
    unitTypes: { label: "Unit Type", activeKey: "is_active" },
    propertyServices: { label: "Property Service", activeKey: "is_active" },
    rentTypes: { label: "Rent Type", activeKey: "is_active" },
    leaseTypes: { label: "Lease Type", activeKey: "is_active" },
    paymentTypes: { label: "Payment Type", activeKey: "is_active" },
    paymentStatuses: { label: "Payment Status", activeKey: "is_active" },
    paymentMethods: { label: "Payment Method", activeKey: "is_active" },
    vendorTypes: { label: "Vendor Type", activeKey: "is_active" },
  };

  // Combine all items into a flat array
  const combinedItems = Object.entries(config).flatMap(([key, { label, activeKey }]) =>
    (allTypes[key] || []).map((item) => ({ ...item, label, activeKey }))
  );


  const typeOptions = [
  { value: "Property_type", label: "Property Type" },
  { value: "Property_status", label: "Property Status" },
  { value: "Property_stage", label: "Property Stages" },
  { value: "Unit_type", label: "Unit Types" },
  { value: "Property_service", label: "Property Services" },
  { value: "Rent_type", label: "Rent Types" },
  { value: "Lease_type", label: "Lease Types" },
  { value: "Payment_type", label: "Payment Types" },
  { value: "Payment_status", label: "Payment Status" },
  { value: "Payment_method", label: "Payment Method" },
  { value: "Vendor_type", label: "Vendor Type" },
];

  const labelToType = {
    "Property Type": "Property_type",
    "Property Status": "Property_status",
    "Property Condition": "Property_stage",
    "Unit Type": "Unit_type",
    "Property Service": "Property_service",
    "Rent Type": "Rent_type",
    "Lease Type": "Lease_type",
    "Payment Type": "Payment_type",
    "Payment Status": "Payment_status",
    "Payment Method": "Payment_method",
    "Vendor Type": "Vendor_type",
  };

  // Filter by search term
  const filteredItems = combinedItems.filter(
    (item) =>
      item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
  const paginatedItems = filteredItems.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePageChange = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };
// handle submit
      const handleSubmit = async (e) => {
      e.preventDefault();

      // Optional validation
      if (!selectedItem.name || !selectedItem.type) {
        alert("Name and Type are required!");
        return;
      }

      try {
        const payload = {
          ...selectedItem,
          is_active: selectedItem.is_active ?? 1, // default active if missing
        };

        if (selectedItem.id) {
          // Update existing type
          await manageTypesAPI.update(selectedItem.id, payload);
          setIsEditModalOpen(false);
        } else {
          // Create new type
          await manageTypesAPI.create(payload);
          setIsAddModalOpen(false);

          // Reset form after adding
          setSelectedItem({
            name: "",
            description: "",
            type: "",
            is_active: 1,
          });
        }

        // Refresh table
        fetchTypes();
      } catch (error) {
        console.error(
          selectedItem.id ? "Error updating type:" : "Error creating type:",
          error
        );
      }
    };

    const handleDelete = async (item) => {
      if (window.confirm(`Delete ${item.name}?`)) {
        try {
          console.log(item); 
          await manageTypesAPI.delete(item.id, labelToType[item.label]);
          fetchTypes();
        } catch (error) {
          console.error("Error deleting type:", error);
        }
      }
    };

  return (
    <Card className="mt-6">
      <CardContent className="space-y-4">
        {/* Header + Search + Add Button */}
        <div className="flex justify-between items-center">
          <Input
            placeholder="Search types..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" /> Add Type
          </Button>
        </div>

        {/* Table */}
        <table className="w-full border-collapse table-auto">
          <thead>
            <tr className="bg-muted">
              <th className="p-4 text-left">Type</th>
              <th className="p-4 text-left">Name</th>
              <th className="p-4 text-left">Description</th>
              <th className="p-4 text-left">Status</th>
              <th className="p-4 text-left">Sort Order</th>
              <th className="p-4 text-right">Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedItems.map((item) => (
              <tr key={`${item.label}-${item.id}`} className="border-b hover:bg-muted/50">
                <td className="p-4">{item.label}</td>
                <td className="p-4">{item.name}</td>
                <td className="p-4 text-muted-foreground">{item.description}</td>
                <td className="p-4">
                  <Badge
                    variant={
                      item.activeKey === "status"
                        ? item.status === "active"
                          ? "default"
                          : "secondary"
                        : item[item.activeKey]
                        ? "default"
                        : "secondary"
                    }
                  >
                    {item.activeKey === "status"
                      ? item.status === "active"
                        ? "Active"
                        : "Inactive"
                      : item[item.activeKey]
                      ? "Active"
                      : "Inactive"}
                  </Badge>
                </td>
                <td className="p-4 text-muted-foreground">{item.sort_order}</td>
                <td className="p-4 text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => {
                          const type = labelToType[item.label];
                          const is_active = item.activeKey === "status" ? (item.status === "active" ? 1 : 0) : item.is_active;
                          setSelectedItem({ ...item, type, is_active });
                          setIsEditModalOpen(true);
                        }}
                      >
                        <Edit2 className="mr-2 h-4 w-4" /> Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDelete(item)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" /> Delete
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Power className="mr-2 h-4 w-4" /> Toggle Status
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2 mt-4">
            <Button onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1}>
              Prev
            </Button>
            {Array.from({ length: totalPages }, (_, i) => (
              <Button
                key={i + 1}
                variant={currentPage === i + 1 ? "default" : "outline"}
                onClick={() => handlePageChange(i + 1)}
              >
                {i + 1}
              </Button>
            ))}
            <Button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}

        {/* Add Modal */}
        {isAddModalOpen && (
          <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-6">
            <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg">
              <div className="flex justify-between items-center border-b pb-2">
                <h3 className="font-semibold">Add New Type</h3>
                <button onClick={() => setIsAddModalOpen(false)}>
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="mt-4 space-y-2">
                {/* Name */}
                <Input
                  value={selectedItem.name}
                  onChange={(e) =>
                    setSelectedItem((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="Name"
                />

                {/* Description */}
                <Input
                  value={selectedItem.description}
                  onChange={(e) =>
                    setSelectedItem((prev) => ({ ...prev, description: e.target.value }))
                  }
                  placeholder="Description"
                />

                {/* Dropdown (Select) */}
                <Select
                  value={selectedItem.type}
                  onValueChange={(value) =>
                    setSelectedItem((prev) => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a type" />
                  </SelectTrigger>
                  <SelectContent>
                    {typeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Status Toggle */}
              <div className="flex items-center space-x-2">
                <Switch
                  checked={selectedItem.is_active === 1}
                  onCheckedChange={(checked) =>
                    setSelectedItem((prev) => ({
                      ...prev,
                      is_active: checked ? 1 : 0,
                    }))
                  }
                />
                <span>{selectedItem.is_active === 1 ? "Active" : "Inactive"}</span>
              </div>
              </div>
              <div className="mt-4 flex justify-end">
                <Button onClick={handleSubmit}>Save</Button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Modal */}
        {isEditModalOpen && selectedItem && (
         <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-6">
            <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg">
              <div className="flex justify-between items-center border-b pb-2">
                <h3 className="font-semibold">Edit Type</h3>
                <button onClick={() => setIsEditModalOpen(false)}>
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="mt-4 space-y-2">
                {/* Name */}
                <Input
                  value={selectedItem.name}
                  onChange={(e) =>
                    setSelectedItem((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="Name"
                />

                {/* Description */}
                <Input
                  value={selectedItem.description}
                  onChange={(e) =>
                    setSelectedItem((prev) => ({ ...prev, description: e.target.value }))
                  }
                  placeholder="Description"
                />

                {/* Type Dropdown */}
                <Select
                  value={selectedItem.type}
                  onValueChange={(value) =>
                    setSelectedItem((prev) => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a type" />
                  </SelectTrigger>
                  <SelectContent>
                    {typeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Status Toggle */}
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={selectedItem.is_active === 1}
                    onCheckedChange={(checked) =>
                      setSelectedItem((prev) => ({
                        ...prev,
                        is_active: checked ? 1 : 0,
                      }))
                    }
                  />
                  <span>{selectedItem.is_active === 1 ? "Active" : "Inactive"}</span>
                </div>
              </div>

              <div className="mt-4 flex justify-end">
                <Button onClick={handleSubmit}>
                  Update
                </Button>
              </div>
            </div>
          </div>

        )}
      </CardContent>
    </Card>
  );
}
