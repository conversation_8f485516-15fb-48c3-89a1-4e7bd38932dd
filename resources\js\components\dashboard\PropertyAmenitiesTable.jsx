import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

import { useTranslation } from '@/hooks/useTranslation';
import { Package, MoreHorizontal, Edit, Trash2, Eye } from "lucide-react";

const PropertyAmenitiesTable = ({
  handleSelectAmenity,
  isSelectAll,
  handleSelectAll,
  selectedAmenities,
  hasPermission,
  filteredAmenities,
  getCategoryIcon,
  openEditDialog,
  openViewDialog,
  handleDelete,
  searchTerm,
  selectedCategory,
  selectedStatus,
  categories
}) => {
  const { t } = useTranslation();

  return (
    <Card>
        <CardContent className="p-0">
          {filteredAmenities.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium">No amenities found</h3>
              <p className="text-muted-foreground">
                {searchTerm || (selectedCategory && selectedCategory !== 'all') || (selectedStatus && selectedStatus !== 'all') 
                  ? 'Try adjusting your filters'
                  : 'Get started by creating your first property amenity'
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr>
                    <th className="text-left p-4">
                      <Checkbox
                        checked={isSelectAll}
                        onCheckedChange={handleSelectAll}
                        disabled={!hasPermission('property-amenity', 'update')}
                      />
                    </th>
                    <th className="text-left p-4 font-medium">Name</th>
                    <th className="text-left p-4 font-medium">Category</th>
                    <th className="text-left p-4 font-medium">Status</th>
                    <th className="text-left p-4 font-medium">Order</th>
                    <th className="text-left p-4 font-medium">Created</th>
                    <th className="text-right p-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAmenities.map((amenity) => (
                    <tr key={amenity.id} className="border-b hover:bg-muted/50">
                      <td className="p-4">
                        <Checkbox
                          checked={selectedAmenities.includes(amenity.id)}
                          onCheckedChange={() => handleSelectAmenity(amenity.id)}
                          disabled={!hasPermission('property-amenity', 'update')}
                        />
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-3">
                          {amenity.icon && (
                            <div className="flex-shrink-0">
                              {getCategoryIcon(amenity.category)}
                            </div>
                          )}
                          <div>
                            <div className="font-medium">{amenity.name}</div>
                            {amenity.description && (
                              <div className="text-sm text-muted-foreground line-clamp-1">
                                {amenity.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          {getCategoryIcon(amenity.category)}
                          <span className="capitalize">{categories[amenity.category]}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge variant={amenity.is_active ? 'default' : 'secondary'}>
                          {amenity.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td className="p-4 text-muted-foreground">
                        {amenity.sort_order}
                      </td>
                      <td className="p-4 text-muted-foreground">
                        {new Date(amenity.created_at).toLocaleDateString()}
                      </td>
                      <td className="p-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openViewDialog(amenity)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </DropdownMenuItem>
                            {hasPermission('property-amenity', 'update') && (
                              <DropdownMenuItem onClick={() => openEditDialog(amenity)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                            )}
                            {hasPermission('property-amenity', 'delete') && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => handleDelete(amenity)}
                                  className="text-destructive"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      
  )};
  export default PropertyAmenitiesTable;