// components/landOwners/LogModal.jsx
import React from 'react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Activity, X, Calendar, User, FileText } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

const LogModal = ({
  isOpen,
  onClose,
  auditLogs,
  loadingLogs,
  fetchAuditLogs
}) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-6xl max-h-[80vh] flex flex-col overflow-hidden">
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <Activity className="h-6 w-6" />
            <div>
              <h3 className="text-lg font-semibold">{t('landOwners.auditLog.title')}</h3>
              <p className="text-blue-100 text-sm">
                {t('landOwners.auditLog.description')}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="flex-1 overflow-auto p-6">
          {loadingLogs ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : auditLogs.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">{t('landOwners.auditLog.noLogs')}</h3>
              <p className="mt-1 text-sm text-gray-500">{t('landOwners.auditLog.noActivity')}</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('landOwners.auditLog.dateTime')}</TableHead>
                  <TableHead>{t('landOwners.auditLog.action')}</TableHead>
                  <TableHead>{t('landOwners.auditLog.landOwner')}</TableHead>
                  <TableHead>{t('landOwners.auditLog.user')}</TableHead>
                  <TableHead>{t('landOwners.auditLog.details')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {auditLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="font-medium">
                            {new Date(log.created_at).toLocaleDateString()}
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(log.created_at).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={
                          log.action === 'created' ? 'default' :
                          log.action === 'updated' ? 'secondary' :
                          log.action === 'deleted' ? 'destructive' : 'outline'
                        }
                        className="capitalize"
                      >
                        {log.action}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {log.land_owner ? 
                          `${log.land_owner.first_name} ${log.land_owner.last_name}` : 
                          'N/A'
                        }
                      </div>
                      {log.land_owner_id && (
                        <div className="text-sm text-gray-500">ID: {log.land_owner_id}</div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="font-medium">{log.user ? log.user.name : 'Unknown'}</div>
                          <div className="text-sm text-gray-500">{log.user ? log.user.email : 'N/A'}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs">
                        <div className="text-sm text-gray-900">{log.description || 'No details'}</div>
                        {log.detailed_changes && log.detailed_changes.length > 0 && (
                          <div className="mt-2 space-y-1">
                            <div className="text-xs font-medium text-gray-700">{t('landOwners.auditLog.changes')}</div>
                            {log.detailed_changes.map((change, idx) => (
                              <div key={idx} className="text-xs bg-gray-50 p-2 rounded border">
                                <div className="font-medium text-gray-800">{change.field}:</div>
                                <div className="flex items-center gap-2 mt-1">
                                  <span className="text-red-600 line-through">{change.old_value}</span>
                                  <span className="text-gray-400">→</span>
                                  <span className="text-green-600">{change.new_value}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
        
        <div className="flex justify-end space-x-2 pt-4 border-t px-6 pb-6">
          <Button 
            variant="outline" 
            onClick={onClose}
            className="hover:bg-gray-50 transition-colors"
          >
            {t('landOwners.auditLog.close')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LogModal;
