

const PropertyServiceModal = ({
  editService ,
  error ,
  form ,
  setForm ,
  saving ,
  handleSave ,
  setModalOpen
}) => {
  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] flex flex-col overflow-hidden">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
              <div>
                <h3 className="text-lg font-semibold">
                  {editService ? 'Edit Property Service' : 'Add Property Service'}
                </h3>
                <p className="text-blue-100 text-sm">
                  {editService ? 'Update property service information' : 'Create a new property service'}
                </p>
              </div>
              <button
                onClick={() => setModalOpen(false)}
                className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
              >
                ×
              </button>
            </div>
            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {error && <p className="text-red-600 mb-4">{error}</p>}
              <form className="space-y-4" onSubmit={e => { e.preventDefault(); handleSave(); }}>
                <div className="space-y-2">
                  <label htmlFor="name" className="block font-medium">Service Name *</label>
                  <input
                    id="name"
                    type="text"
                    value={form.name}
                    onChange={e => setForm({ ...form, name: e.target.value })}
                    placeholder="e.g., Rent, Sale, Lease"
                    required
                    disabled={saving}
                    className="border rounded px-2 py-1 w-full"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="description" className="block font-medium">Description</label>
                  <textarea
                    id="description"
                    value={form.description}
                    onChange={e => setForm({ ...form, description: e.target.value })}
                    placeholder="Brief description of this service"
                    rows={3}
                    disabled={saving}
                    className="border rounded px-2 py-1 w-full"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    id="is_active"
                    type="checkbox"
                    checked={form.is_active}
                    onChange={e => setForm({ ...form, is_active: e.target.checked })}
                    disabled={saving}
                    className="rounded"
                  />
                  <label htmlFor="is_active" className="font-medium">Active Status</label>
                </div>
                <div className="flex justify-end space-x-2 mt-6">
                  <button
                    type="button"
                    onClick={() => setModalOpen(false)}
                    className="px-4 py-2 bg-gray-300 rounded"
                    disabled={saving}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded"
                    disabled={saving}
                  >
                    {saving ? (editService ? 'Updating...' : 'Creating...') : (editService ? 'Update Service' : 'Create Service')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
  )};
export default PropertyServiceModal;