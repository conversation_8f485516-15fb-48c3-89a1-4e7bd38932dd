{"name": "laravel-react-dashboard", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.3.2", "jquery": "^3.7.1", "jquery-ui": "^1.14.1", "lucide-react": "^0.523.0", "quill": "^2.0.3", "react": "^18.2.0", "react-day-picker": "^9.9.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.6.0", "react-i18next": "^15.6.0", "react-quill": "^2.0.0", "react-router-dom": "^7.6.2", "recharts": "^3.0.0", "sweetalert2": "^11.22.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "laravel-vite-plugin": "^0.8.1", "postcss": "^8.4.24", "tailwindcss": "^3.3.2", "tailwindcss-animate": "^1.0.7", "vite": "^4.3.9"}}