<?php

namespace App\Http\Controllers;
use App\Models\LeaseType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class LeaseTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try{
            $query = LeaseType::query();
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where('name', 'LIKE', "%{$search}%");
            }
            $perPage = $request->get('per_page', 10);
            $leaseTypes = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $leaseTypes
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve lease types: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try{
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:lease_types,name',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $leaseType = LeaseType::create($validated);
            return response()->json([
                'success' => true,
                'data' => $leaseType,
                'message' => 'Lease type created successfully'
            ], 201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to create lease type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        //
        try{
            $leaseType = LeaseType::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $leaseType,
                'message' => 'Lease type retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve lease type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        //
        try{
            $leaseType = LeaseType::findOrFail($id);
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:lease_types,name,' . $id,
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $leaseType->update($validated);
            return response()->json([
                'success' => true,
                'data' => $leaseType->fresh(),
                'message' => 'Lease type updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update lease type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        //
        try{
            $leaseType = LeaseType::findOrFail($id);
            $leaseType->delete();
            return response()->json([
                'success' => true,
                'message' => 'Lease type deleted successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete lease type: ' . $e->getMessage()
            ], 500);
        }
    }
    public function dropdown(): JsonResponse
    {
        try{
            $leaseTypes = LeaseType::active()
                ->ordered()
                ->select('id', 'name', 'description')
                ->get();
            return response()->json([
                'success' => true,
                'data' => $leaseTypes,
                'message' => 'Lease types for dropdown retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve lease types for dropdown',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function toggleStatus(string $id): JsonResponse
    {
        try{    
            $leaseType = LeaseType::findOrFail($id);
            $leaseType->is_active = !$leaseType->is_active;
            $leaseType->save();
            return response()->json([
                'success' => true,
                'data' => $leaseType,
                'message' => 'Lease type status updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update lease type status',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
