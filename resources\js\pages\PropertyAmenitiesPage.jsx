import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/contexts/AuthContext';
import { showAlertMethods as showAlert } from '@/utils/sweetAlert';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  Power,
  Settings,
  Home,
  Shield,
  Car,
  Wrench,
  Package,
  ChevronUp,
  ChevronDown,
  Loader2,
  X
} from 'lucide-react';

const PropertyAmenitiesPage = () => {
  const { user, hasPermission } = useAuth();
  const [amenities, setAmenities] = useState([]);
  const [filteredAmenities, setFilteredAmenities] = useState([]);
  const [categories, setCategories] = useState({});
  const [icons, setIcons] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [sortBy, setSortBy] = useState('sort_order');
  const [sortOrder, setSortOrder] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [perPage] = useState(15);
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedAmenity, setSelectedAmenity] = useState(null);
  
  // Form states
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: '',
    category: 'basic',
    is_active: true,
    sort_order: 0
  });
  const [formErrors, setFormErrors] = useState({});
  
  // Bulk actions
  const [selectedAmenities, setSelectedAmenities] = useState([]);
  const [isSelectAll, setIsSelectAll] = useState(false);

  // Category icons mapping
  const categoryIcons = {
    basic: Home,
    recreational: Package,
    security: Shield,
    parking: Car,
    utilities: Power,
    maintenance: Wrench,
    other: Settings
  };

  // Load amenities data
  const loadAmenities = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage,
        per_page: perPage,
        sort_by: sortBy,
        sort_order: sortOrder,
        ...(searchTerm && { search: searchTerm }),
        ...(selectedCategory && selectedCategory !== 'all' && { category: selectedCategory }),
        ...(selectedStatus && selectedStatus !== 'all' && { status: selectedStatus })
      });

      const response = await fetch(`/api/property-amenities?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch amenities');
      }

      const data = await response.json();
      if (data.success) {
        // Ensure amenities is always an array
        const amenitiesData = data.data?.data || data.data || [];
        setAmenities(Array.isArray(amenitiesData) ? amenitiesData : []);
        setCategories(data.categories || {});
        setIcons(data.icons || {});
        setTotalPages(data.data?.last_page || 1);
        setCurrentPage(data.data?.current_page || 1);
      } else {
        // Handle unsuccessful response
        setAmenities([]);
      }
    } catch (error) {
      console.error('Error loading amenities:', error);
      setAmenities([]); // Ensure amenities is always an array
      showAlert.error('Error', 'Failed to load property amenities');
    } finally {
      setLoading(false);
    }
  };

  // Filter amenities based on search and filters
  useEffect(() => {
    // Ensure amenities is an array before filtering
    if (!Array.isArray(amenities)) {
      setFilteredAmenities([]);
      return;
    }

    let filtered = amenities;

    if (searchTerm) {
      filtered = filtered.filter(amenity =>
        amenity.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        amenity.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        amenity.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredAmenities(filtered);
  }, [amenities, searchTerm]);

  // Load data on component mount and when filters change
  useEffect(() => {
    if (hasPermission('property-amenity', 'read')) {
      loadAmenities();
    }
  }, [currentPage, perPage, sortBy, sortOrder, selectedCategory, selectedStatus, hasPermission]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!hasPermission('property-amenity', isEditDialogOpen ? 'update' : 'create')) return;

    try {
      setSaving(true);
      setFormErrors({});

      const url = isEditDialogOpen 
        ? `/api/property-amenities/${selectedAmenity.id}`
        : '/api/property-amenities';
      
      const method = isEditDialogOpen ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Accept': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        showAlert.success(
          'Success',
          `Property amenity ${isEditDialogOpen ? 'updated' : 'created'} successfully`
        );
        setIsCreateDialogOpen(false);
        setIsEditDialogOpen(false);
        resetForm();
        loadAmenities();
      } else {
        if (data.errors) {
          setFormErrors(data.errors);
        } else {
          showAlert.error('Error', data.message || 'Failed to save amenity');
        }
      }
    } catch (error) {
      console.error('Error saving amenity:', error);
      showAlert.error('Error', 'Failed to save property amenity');
    } finally {
      setSaving(false);
    }
  };

  // Handle delete
  const handleDelete = async (amenity) => {
    if (!hasPermission('property-amenity', 'delete')) return;

    const result = await showAlert.confirm(
      'Delete Amenity',
      `Are you sure you want to delete "${amenity.name}"? This action cannot be undone.`,
      'Delete',
      'Cancel'
    );

    if (result.isConfirmed) {
      try {
        const response = await fetch(`/api/property-amenities/${amenity.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            'Accept': 'application/json'
          }
        });

        const data = await response.json();
        if (data.success) {
          showAlert.success('Success', 'Property amenity deleted successfully');
          loadAmenities();
        } else {
          showAlert.error('Error', data.message || 'Failed to delete amenity');
        }
      } catch (error) {
        console.error('Error deleting amenity:', error);
        showAlert.error('Error', 'Failed to delete property amenity');
      }
    }
  };

  // Handle bulk status update
  const handleBulkStatusUpdate = async (isActive) => {
    if (!hasPermission('property-amenity', 'update') || selectedAmenities.length === 0) return;

    try {
      const response = await fetch('/api/property-amenities/bulk-status', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          ids: selectedAmenities,
          is_active: isActive
        })
      });

      const data = await response.json();
      if (data.success) {
        showAlert.success('Success', `Amenities ${isActive ? 'activated' : 'deactivated'} successfully`);
        setSelectedAmenities([]);
        setIsSelectAll(false);
        loadAmenities();
      } else {
        showAlert.error('Error', data.message || 'Failed to update amenities');
      }
    } catch (error) {
      console.error('Error updating amenities:', error);
      showAlert.error('Error', 'Failed to update amenities status');
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      icon: '',
      category: 'basic',
      is_active: true,
      sort_order: 0
    });
    setFormErrors({});
    setSelectedAmenity(null);
  };

  // Open edit dialog
  const openEditDialog = (amenity) => {
    setSelectedAmenity(amenity);
    setFormData({
      name: amenity.name,
      description: amenity.description || '',
      icon: amenity.icon || '',
      category: amenity.category,
      is_active: amenity.is_active,
      sort_order: amenity.sort_order
    });
    setIsEditDialogOpen(true);
  };

  // Open view dialog
  const openViewDialog = (amenity) => {
    setSelectedAmenity(amenity);
    setIsViewDialogOpen(true);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (isSelectAll) {
      setSelectedAmenities([]);
      setIsSelectAll(false);
    } else {
      setSelectedAmenities(filteredAmenities.map(a => a.id));
      setIsSelectAll(true);
    }
  };

  // Handle individual select
  const handleSelectAmenity = (amenityId) => {
    if (selectedAmenities.includes(amenityId)) {
      setSelectedAmenities(selectedAmenities.filter(id => id !== amenityId));
      setIsSelectAll(false);
    } else {
      const newSelected = [...selectedAmenities, amenityId];
      setSelectedAmenities(newSelected);
      setIsSelectAll(newSelected.length === filteredAmenities.length);
    }
  };

  // Get category icon
  const getCategoryIcon = (category) => {
    const IconComponent = categoryIcons[category] || Settings;
    return <IconComponent className="h-4 w-4" />;
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading property amenities...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Property Amenities</h1>
          <p className="text-muted-foreground">
            Manage property amenities and features
          </p>
        </div>
        {hasPermission('property-amenity', 'create') && (
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Amenity
          </Button>
        )}
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search amenities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {Object.entries(categories).map(([key, label]) => (
                  <SelectItem key={key} value={key}>
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(key)}
                      {label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="1">Active</SelectItem>
                <SelectItem value="0">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          {selectedAmenities.length > 0 && hasPermission('property-amenity', 'update') && (
            <div className="flex items-center gap-2 mt-4 p-3 bg-muted rounded-lg">
              <span className="text-sm text-muted-foreground">
                {selectedAmenities.length} selected
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkStatusUpdate(true)}
              >
                Activate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkStatusUpdate(false)}
              >
                Deactivate
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Amenities List */}
      <Card>
        <CardContent className="p-0">
          {filteredAmenities.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium">No amenities found</h3>
              <p className="text-muted-foreground">
                {searchTerm || (selectedCategory && selectedCategory !== 'all') || (selectedStatus && selectedStatus !== 'all') 
                  ? 'Try adjusting your filters'
                  : 'Get started by creating your first property amenity'
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr>
                    <th className="text-left p-4">
                      <Checkbox
                        checked={isSelectAll}
                        onCheckedChange={handleSelectAll}
                        disabled={!hasPermission('property-amenity', 'update')}
                      />
                    </th>
                    <th className="text-left p-4 font-medium">Name</th>
                    <th className="text-left p-4 font-medium">Category</th>
                    <th className="text-left p-4 font-medium">Status</th>
                    <th className="text-left p-4 font-medium">Order</th>
                    <th className="text-left p-4 font-medium">Created</th>
                    <th className="text-right p-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAmenities.map((amenity) => (
                    <tr key={amenity.id} className="border-b hover:bg-muted/50">
                      <td className="p-4">
                        <Checkbox
                          checked={selectedAmenities.includes(amenity.id)}
                          onCheckedChange={() => handleSelectAmenity(amenity.id)}
                          disabled={!hasPermission('property-amenity', 'update')}
                        />
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-3">
                          {amenity.icon && (
                            <div className="flex-shrink-0">
                              {getCategoryIcon(amenity.category)}
                            </div>
                          )}
                          <div>
                            <div className="font-medium">{amenity.name}</div>
                            {amenity.description && (
                              <div className="text-sm text-muted-foreground line-clamp-1">
                                {amenity.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          {getCategoryIcon(amenity.category)}
                          <span className="capitalize">{categories[amenity.category]}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge variant={amenity.is_active ? 'default' : 'secondary'}>
                          {amenity.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td className="p-4 text-muted-foreground">
                        {amenity.sort_order}
                      </td>
                      <td className="p-4 text-muted-foreground">
                        {new Date(amenity.created_at).toLocaleDateString()}
                      </td>
                      <td className="p-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openViewDialog(amenity)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </DropdownMenuItem>
                            {hasPermission('property-amenity', 'update') && (
                              <DropdownMenuItem onClick={() => openEditDialog(amenity)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                            )}
                            {hasPermission('property-amenity', 'delete') && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => handleDelete(amenity)}
                                  className="text-destructive"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-3 text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Create/Edit Modal */}
      {(isCreateDialogOpen || isEditDialogOpen) && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
              <div className="flex items-center space-x-3">
                <Package className="h-6 w-6" />
                <div>
                  <h3 className="text-lg font-semibold">
                    {isEditDialogOpen ? 'Edit Amenity' : 'Create New Amenity'}
                  </h3>
                  <p className="text-blue-100 text-sm">
                    {isEditDialogOpen 
                      ? 'Update the amenity information below.'
                      : 'Fill in the details to create a new property amenity.'
                    }
                  </p>
                </div>
              </div>
              <button
                onClick={() => {
                  setIsCreateDialogOpen(false);
                  setIsEditDialogOpen(false);
                  resetForm();
                }}
                className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="no-focus-outline">
                    <Label htmlFor="name">Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter amenity name"
                      className={formErrors.name ? 'border-destructive' : ''}
                    />
                    {formErrors.name && (
                      <p className="text-sm text-destructive mt-1">{formErrors.name[0]}</p>
                    )}
                  </div>
                  
                  <div className="no-focus-outline">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter amenity description"
                      rows={3}
                      className={formErrors.description ? 'border-destructive' : ''}
                    />
                    {formErrors.description && (
                      <p className="text-sm text-destructive mt-1">{formErrors.description[0]}</p>
                    )}
                  </div>

                  <div className='no-focus-outline'>
                    <Label htmlFor="category">Category *</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => setFormData({ ...formData, category: value })}
                    >
                      <SelectTrigger className={formErrors.category ? 'border-destructive' : ''}>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(categories).map(([key, label]) => (
                          <SelectItem key={key} value={key}>
                            <div className="flex items-center gap-2">
                              {getCategoryIcon(key)}
                              {label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formErrors.category && (
                      <p className="text-sm text-destructive mt-1">{formErrors.category[0]}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="sort_order">Sort Order</Label>
                    <Input
                      id="sort_order"
                      type="number"
                      min="0"
                      value={formData.sort_order}
                      onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
                      placeholder="0"
                      className={formErrors.sort_order ? 'border-destructive' : ''}
                    />
                    {formErrors.sort_order && (
                      <p className="text-sm text-destructive mt-1">{formErrors.sort_order[0]}</p>
                    )}
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor="icon">Icon</Label>
                    <Select
                      value={formData.icon || "no-icon"}
                      onValueChange={(value) => setFormData({ ...formData, icon: value === "no-icon" ? "" : value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select icon (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="no-icon">No Icon</SelectItem>
                        {Object.entries(icons).map(([key, label]) => (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="col-span-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="is_active"
                        checked={formData.is_active}
                        onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                      />
                      <Label htmlFor="is_active">Active</Label>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsCreateDialogOpen(false);
                      setIsEditDialogOpen(false);
                      resetForm();
                    }}
                    disabled={saving}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={saving}
                    className="bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    {saving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    {isEditDialogOpen ? 'Update' : 'Create'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {isViewDialogOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
              <div className="flex items-center space-x-3">
                <Eye className="h-6 w-6" />
                <div>
                  <h3 className="text-lg font-semibold">Amenity Details</h3>
                  <p className="text-blue-100 text-sm">
                    View amenity information
                  </p>
                </div>
              </div>
              <button
                onClick={() => setIsViewDialogOpen(false)}
                className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {selectedAmenity && (
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Name</Label>
                    <p className="text-sm">{selectedAmenity.name}</p>
                  </div>
                  
                  {selectedAmenity.description && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                      <p className="text-sm">{selectedAmenity.description}</p>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Category</Label>
                      <div className="flex items-center gap-2 mt-1">
                        {getCategoryIcon(selectedAmenity.category)}
                        <span className="text-sm capitalize">{categories[selectedAmenity.category]}</span>
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                      <div className="mt-1">
                        <Badge variant={selectedAmenity.is_active ? 'default' : 'secondary'}>
                          {selectedAmenity.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Sort Order</Label>
                      <p className="text-sm">{selectedAmenity.sort_order}</p>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Created</Label>
                      <p className="text-sm">{new Date(selectedAmenity.created_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                  
                  {selectedAmenity.creator && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Created By</Label>
                      <p className="text-sm">{selectedAmenity.creator.name}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
            
            <div className="flex justify-end space-x-2 pt-4 border-t px-6 pb-6">
              <Button 
                variant="outline" 
                onClick={() => setIsViewDialogOpen(false)}
                className="hover:bg-gray-50 transition-colors"
              >
                Close
              </Button>
              {hasPermission('property-amenity', 'update') && selectedAmenity && (
                <Button 
                  onClick={() => {
                    setIsViewDialogOpen(false);
                    openEditDialog(selectedAmenity);
                  }}
                  className="bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PropertyAmenitiesPage;
