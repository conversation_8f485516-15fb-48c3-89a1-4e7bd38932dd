<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RentType extends Model
{
    //
    protected $fillable = [
        'name',
        'description',
        'is_active',
      
    ];

    
    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
