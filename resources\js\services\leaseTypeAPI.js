import axios from "axios";

const API_URL = '/api/lease-types';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add request interceptor to include auth token 
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // Handle unauthorized access
            localStorage.removeItem('auth_token');
            // Redirect to login if needed
        }
        return Promise.reject(error);
    }
);

// Lease Type API functions
export const leaseTypeAPI = {
    // Get all lease types with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            // Handle paginated response
            if (response.data.success && response.data.data && response.data.data.data) {
                return response.data; // Return full response for pagination info
            }
            return response.data;
        } catch (error) {
            console.error('Error fetching lease types:', error);
            throw error.response?.data || error;
        }
    },

    // Get lease type by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching lease type:', error);
            throw error.response?.data || error;
        }
    },

    // Create new lease type
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error creating lease type:', error);
            throw error.response?.data || error;
        }
    },

    // Update lease type
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error updating lease type:', error);
            throw error.response?.data || error;
        }
    },

    // Delete lease type
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error deleting lease type:', error);
            throw error.response?.data || error;
        }
    },

    // Toggle lease type status (active/inactive)
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error toggling lease type status:', error);
            throw error.response?.data || error;
        }
    },

    // Get dropdown options (active lease types only)
    getDropdown: async () => {
        try {
            const response = await api.get(`${API_URL}-dropdown`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching lease types dropdown:', error);
            throw error.response?.data || error;
        }
    }
}

export default leaseTypeAPI;
