import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, Edit2, ToggleRight, ToggleLeft, Trash2 } from "lucide-react";
import propertyTypeAPI from "../../services/propertyTypeAPI";
import { showAlert } from "../../utils/sweetAlert";

const PropertyTypeTable = ({
  searchTerm = "",
  statusFilter = "all",
  openModal = () => {},
  handleToggleStatus = () => {},
  handleDelete = () => {},
}) => {
  const [propertyTypes, setPropertyTypes] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchPropertyTypes = async () => {
    setLoading(true);
    try {
      const params = {
        search: searchTerm || undefined,
        is_active:
          statusFilter === "all" ? undefined : statusFilter === "active",
      };
      const response = await propertyTypeAPI.getAll(params);
      if (response.success) {
        setPropertyTypes(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching property types:", error);
      showAlert("error", "Failed to fetch property types");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPropertyTypes();
  }, [searchTerm, statusFilter]);

  const filteredTypes = propertyTypes.filter((type) => {
    const matchesSearch =
      !searchTerm ||
      type.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" ? type.is_active : !type.is_active);
    return matchesSearch && matchesStatus;
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Property Types ({filteredTypes.length})</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredTypes.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTypes.map((type) => (
              <Card key={type.id} className="shadow-md border rounded-xl">
                <CardHeader className="flex justify-between items-center">
                  <span className="font-semibold text-lg">{type.name}</span>
                  <Badge
                    variant={type.is_active ? "default" : "secondary"}
                    className={type.is_active ? "bg-green-500" : "bg-gray-500"}
                  >
                    {type.is_active ? "Active" : "Inactive"}
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-2">
                  {type.description && (
                    <p className="text-sm text-gray-500 line-clamp-2">
                      {type.description}
                    </p>
                  )}
                  <div className="flex flex-wrap gap-2 text-sm">
                    <span className="text-gray-600">Order: {type.sort_order}</span>
                  </div>
                  {/* Actions */}
                  <div className="flex justify-end gap-2 pt-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openModal("view", type)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openModal("edit", type)}
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleStatus(type)}
                    >
                      {type.is_active ? (
                        <ToggleRight className="h-4 w-4 text-green-600" />
                      ) : (
                        <ToggleLeft className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(type)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No property types found
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PropertyTypeTable;
