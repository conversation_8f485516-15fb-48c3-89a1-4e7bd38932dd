import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle,CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Edit2, Trash2, Eye, Building2, MapPin, Camera, Video } from 'lucide-react';
import { Badge } from "@/components/ui/badge";

const ProjectTable = ({
  projects,
  loading,
  currentPage,
  totalPages,
  fetchProjects,
  handleEdit,
  handleView,
  handleDelete,
  getStatusBadgeVariant,
  propertyTypes,
  propertyStatuses,
  stripHtml

}) => {
  return (
    <Card>
        <CardHeader>
          <CardTitle>Properties</CardTitle>
          <CardDescription>
            Manage your property listings and details
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="space-y-4">
              {projects.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No properties found</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {projects.map((project) => (
                    <Card key={project.id} className="hover:shadow-md transition-shadow h-full flex flex-col">
                      <CardContent className="pt-6 flex-1 flex flex-col">
                        {/* Project Image */}
                        <div className="flex-shrink-0 mb-4">
                          {project.featured_image ? (
                            <img
                              src={project.featured_image.image_url}
                              alt={project.title}
                              className="w-full h-32 object-cover rounded-lg"
                            />
                          ) : (
                            <div className="w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                              <Building2 className="h-8 w-8 text-gray-400" />
                            </div>
                          )}
                        </div>
                        {/* Project Details */}
                        <div className="flex-1 flex flex-col justify-between space-y-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="text-lg font-semibold">{project.title}</h3>
                              <p className="text-sm text-gray-600 flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                {project.location}
                              </p>
                            </div>
                            <div className="flex gap-2">
                              <Badge variant={getStatusBadgeVariant(project.status)}>
                                {propertyStatuses[project.status]}
                              </Badge>
                              {project.is_featured && (
                                <Badge variant="secondary">Featured</Badge>
                              )}
                            </div>
                          </div>
                          <p className="text-sm text-gray-700 line-clamp-2">
                            {stripHtml(project.description)}
                          </p>
                          <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <Building2 className="h-4 w-4" />
                              {propertyTypes[project.property_type]}
                            </span>
                            {project.area_sqft && (
                              <span>{project.area_sqft} sq ft</span>
                            )}
                            {project.bedrooms && (
                              <span>{project.bedrooms} beds</span>
                            )}
                            {project.bathrooms && (
                              <span>{project.bathrooms} baths</span>
                            )}
                            {project.units_count && (
                              <span>{project.units_count} units</span>
                            )}
                          </div>
                          <div className="flex justify-between items-center pt-2">
                            <div className="flex gap-2">
                              {project.images_count > 0 && (
                                <Badge variant="outline" className="text-xs">
                                  <Camera className="h-3 w-3 mr-1" />
                                  {project.images_count}
                                </Badge>
                              )}
                              {project.videos_count > 0 && (
                                <Badge variant="outline" className="text-xs">
                                  <Video className="h-3 w-3 mr-1" />
                                  {project.videos_count}
                                </Badge>
                              )}
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleView(project)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEdit(project)}
                              >
                                <Edit2 className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDelete(project)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-6">
                  <Button
                    variant="outline"
                    onClick={() => fetchProjects(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => fetchProjects(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
  )};

export default ProjectTable;
