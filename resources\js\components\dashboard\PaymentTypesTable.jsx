import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';import {
  MoreHorizontal,
  Edit2,Power ,Trash2
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const PaymentTypeTable = ({ filteredPaymentTypes, handleEdit, handleToggleStatus, handleDelete }) => {
  return (
     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPaymentTypes.map((paymentType) => (
            <Card key={paymentType.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg text-gray-900 mb-2">
                      {paymentType.name}
                    </h3>
                    {paymentType.description && (
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {paymentType.description}
                      </p>
                    )}
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleEdit(paymentType)}>
                        <Edit2 className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleToggleStatus(paymentType)}>
                        <Power className="mr-2 h-4 w-4" />
                        {paymentType.is_active ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDelete(paymentType)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="flex justify-between items-center">
                  <Badge
                    variant={paymentType.is_active ? 'default' : 'secondary'}
                    className={paymentType.is_active ? 'bg-green-500' : 'bg-gray-500'}
                  >
                    {paymentType.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                  <div className="text-sm text-gray-500">
                    Order: {paymentType.sort_order}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
  )};
  export default PaymentTypeTable;
