import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  Plus,
  Edit2,
  Trash2,
  Power,
  MoreHorizontal,
  Settings,
  AlertCircle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import propertyStagesAPI from '../../services/propertyStagesAPI';
import { useAuth } from '../../contexts/AuthContext';
// Simple inline modal for demonstration
function PropertyStageModal({ isOpen, onClose, onSave, stage, mode }) {
  const [formData, setFormData] = useState(
    stage || { name: '', description: '', is_active: true }
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-md max-h-[90vh] flex flex-col overflow-hidden">
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <Settings className="h-6 w-6" />
            <div>
              <h3 className="text-lg font-semibold">
                {mode === 'create'
                  ? 'Add Property Stage'
                  : mode === 'edit'
                  ? 'Edit Property Stage'
                  : 'View Property Stage'}
              </h3>
              <p className="text-blue-100 text-sm">
                {mode === 'create'
                  ? 'Create a new property stage'
                  : mode === 'edit'
                  ? 'Update property stage information'
                  : 'View property stage details'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            {/* X icon, import if not already */}
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>
        {/* Modal Content */}
        <div className="flex-1 flex flex-col overflow-y-auto px-6 py-4 min-h-0">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label className="text-right text-sm font-medium col-span-1">Name</label>
              <Input
                value={formData.name}
                disabled={mode === 'view'}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label className="text-right text-sm font-medium col-span-1">Description</label>
              <textarea
                className="col-span-3 w-full border rounded p-2"
                rows={3}
                value={formData.description}
                disabled={mode === 'view'}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
              />
            </div>
            {mode !== 'view' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <label className="text-right text-sm font-medium col-span-1 flex items-center">Active</label>
                <div className="col-span-3 flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) =>
                      setFormData({ ...formData, is_active: e.target.checked })
                    }
                  />
                  <span>Active</span>
                </div>
              </div>
            )}
          </div>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            {mode !== 'view' && (
              <Button
                onClick={() => {
                  onSave(formData);
                  onClose();
                }}
              >
                Save
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}


export default function PropertyStagePage() {
  const { hasPermission, isAuthenticated } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [stages, setStages] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedStage, setSelectedStage] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch all property condition
  const fetchPropertyStages = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user has permission to read property condition
      if (!hasPermission('property-stage', 'read')) {
        setError('You do not have permission to view property condition.');
        return;
      }

      const response = await propertyStagesAPI.getAll();

      // Always extract the array from the response (handles paginated and non-paginated)
      if (response.success) {
        let data = response.data;
        if (data && typeof data === 'object' && Array.isArray(data.data)) {
          data = data.data; // Laravel paginator: { data: [...] }
        }
        setStages(Array.isArray(data) ? data : []);
      } else {
        setError(response.message || 'Failed to fetch property condition');
      }
    } catch (error) {
      console.error('Error fetching property condition:', error);
      if (error.status === 401) {
        setError('Authentication required. Please log in again.');
      } else if (error.status === 403) {
        setError('You do not have permission to view property condition.');
      } else {
        setError(error.message || 'Failed to fetch property condition. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchPropertyStages();
    }
  }, [isAuthenticated]);


  const handleCreate = () => {
    if (!hasPermission('property-stage', 'create')) {
      setError('You do not have permission to create property condition.');
      return;
    }
    setSelectedStage(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleEdit = (stage) => {
    if (!hasPermission('property-stage', 'update')) {
      setError('You do not have permission to edit property condition.');
      return;
    }
    setSelectedStage(stage);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleView = (stage) => {
    setSelectedStage(stage);
    setModalMode('view');
    setIsModalOpen(true);
  };

  const handleDelete = async (stage) => {
    if (!hasPermission('property-stage', 'delete')) {
      setError('You do not have permission to delete property condition.');
      return;
    }
    if (!window.confirm(`Delete stage "${stage.name}"?`)) return;

    try {
      const response = await propertyStagesAPI.delete(stage.id);
      if (response.success) {
        fetchPropertyStages(); // Refresh the data
        setError(null);
      } else {
        setError(response.message || 'Failed to delete property condition.');
      }
    } catch (err) {
      setError('Failed to delete property stage.');
    }
  };

  const handleToggleStatus = async (stage) => {
    if (!hasPermission('property-stage', 'update')) {
      setError('You do not have permission to update property condition.');
      return;
    }

    try {
      const response = await propertyStagesAPI.toggleStatus(stage.id);
      if (response.success) {
        fetchPropertyStages(); // Refresh the data
        setError(null);
      } else {
        setError(response.message || 'Failed to update status.');
      }
    } catch (err) {
      setError('Failed to update status.');
    }
  };

  const handleSave = async (formData) => {
    try {
      let response;
      if (modalMode === 'create') {
        response = await propertyStagesAPI.create(formData);
      } else if (modalMode === 'edit') {
        response = await propertyStagesAPI.update(selectedStage.id, formData);
      }

      if (response.success) {
        fetchPropertyStages(); // Refresh the data
        setIsModalOpen(false);
        setError(null);
      } else {
        setError(response.message || 'Failed to save property stage.');
      }
    } catch (err) {
      setError('Failed to save property stage.');
    }
  };

  const filteredStages = stages.filter((stage) => {
    const matchesSearch = stage.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && stage.is_active) ||
      (statusFilter === 'inactive' && !stage.is_active);
    return matchesSearch && matchesStatus;
  });

  // Show loading state
  if (loading && stages.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Property Condition</h1>
            <p className="text-muted-foreground">
              Manage your property stage categories
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="text-center py-12">
            <div className="mx-auto h-12 w-12 text-gray-400 mb-4 animate-spin">⟳</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading property conditions...</h3>
            <p className="text-gray-500">Please wait while we fetch your data.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state
  if (error && stages.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Property Condition</h1>
            <p className="text-muted-foreground">
              Manage your property stage categories
            </p>
          </div>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={fetchPropertyStages}
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => setError(null)}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Property Condition</h1>
          <p className="text-muted-foreground">
            Manage your property stage categories
          </p>
        </div>
        <Button
          className="gap-2"
          onClick={handleCreate}
          disabled={!hasPermission('property-stage', 'create')}
        >
          <Plus className="h-4 w-4" />
          Add Stage
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search stages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Stages List */}
  {!loading && filteredStages.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStages.map((stage) => (
            <Card key={stage.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="font-semibold text-lg text-gray-900">
                    {stage.name}
                  </h3>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleView(stage)}>
                        <Settings className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(stage)}>
                        <Edit2 className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleToggleStatus(stage)}>
                        <Power className="mr-2 h-4 w-4" />
                        {stage.is_active ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDelete(stage)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  {stage.description || 'No description provided.'}
                </p>
                <Badge
                  variant={stage.is_active ? 'default' : 'secondary'}
                  className={stage.is_active ? 'bg-green-500' : 'bg-gray-500'}
                >
                  {stage.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No property condition found
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all'
                ? 'No stages match your current filters.'
                : 'Get started by creating your first stage.'}
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button className="gap-2" onClick={handleCreate}>
                <Plus className="h-4 w-4" />
                Add Stage
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Modal */}
      <PropertyStageModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSave}
        stage={selectedStage}
        mode={modalMode}
      />
    </div>
  );
}
