import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

import { Badge, Edit2, ToggleRight, Trash2, MoreHorizontal, Settings } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, } from "@/components/ui/dropdown-menu";
const UnitTypeTable = ({
   filteredUnitTypes,
    handleView,
    handleEdit,
    handleToggleStatus,
    handleDelete,
    hasPermission,
}) => {
  return (
     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUnitTypes.map((unit) => (
            <Card 
              key={unit.id} 
              className={`hover:shadow-lg transition-shadow border ${unit.color}`}
            >
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="font-semibold text-lg text-gray-900">
                    {unit.name}
                  </h3>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem key={`view-${unit.id}`} onClick={() => handleView(unit)}>
                        <Settings className="mr-2 h-4 w-4" />
                        View Details   
                      </DropdownMenuItem>
                      {hasPermission('unit-type', 'update') && (
                        <React.Fragment key={`update-actions-${unit.id}`}>
                          <DropdownMenuItem key={`edit-${unit.id}`} onClick={() => handleEdit(unit)}>
                            <Edit2 className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator key={`sep-${unit.id}`} />
                          <DropdownMenuItem key={`toggle-${unit.id}`} onClick={() => handleToggleStatus(unit)}>
                            <ToggleRight className="mr-2 h-4 w-4" />
                            {unit.is_active ? 'Deactivate' : 'Activate'}
                          </DropdownMenuItem>
                        </React.Fragment>
                      )}
                      {hasPermission('unit-type', 'delete') && (
                        <DropdownMenuItem key={`delete-${unit.id}`} className="text-red-600" onClick={() => handleDelete(unit)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <Badge
                  variant="secondary"
                  className={unit.is_active ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'}
                >
                  {unit.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>
  )};   

export default UnitTypeTable;