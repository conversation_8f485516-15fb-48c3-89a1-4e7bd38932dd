
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import RichTextEditor from '../ui/CompatibleRichTextEditor';
import { showAlert } from '../../utils/sweetAlert';
import { 
  Loader2, Plus, Trash2, Upload, Play, Youtube, 
  Building2, Home, MapPin, Image as ImageIcon, Video, X 
} from 'lucide-react';
import projectAPI from '@/services/projectAPI';
import propertyTypeAPI from '../../services/propertyTypeAPI';
import propertyStatusAPI from '../../services/propertyStatusAPI';
import { countryAPI } from '../../services/countryAPI';
import { stateAPI } from '../../services/stateAPI';
import { locationAPI } from '../../services/locationAPI';
import propertyStagesAPI from '../../services/propertyStagesAPI';
import unitTypeAPI from '../../services/unitTypeAPI';
import rentTypeAPI from '@/services/rentTypeAPI';  // Rent type dropdown state
import leaseTypeAPI from '@/services/leaseTypeAPI'; // Lease type dropdown state

const ProjectModal = ({ isOpen, onClose, project, mode, onSuccess }) => {
  // Document management state and functions
  const 
[documents, setDocuments] = useState([]);
  const [documentFiles, setDocumentFiles] = useState([]);

  const addDocument = () => {
    setDocuments(prev => [
      { id: Date.now(), title: '', show_on_frontend: false, file: null },
      ...prev
    ]);
  };
  
  const [rentTypes, setRentTypes] = useState([]);
  const [leaseTypes, setLeaseTypes] = useState([]);
  // Fetch lease types for dropdown
  useEffect(() => {
    const fetchLeaseTypes = async () => {
      try {
        const result = await leaseTypeAPI.getDropdown();
        if (Array.isArray(result)) {
          setLeaseTypes(result);
        } else if (result && result.success && Array.isArray(result.data)) {
          setLeaseTypes(result.data);
        } else {
          setLeaseTypes([]);
        }
      } catch (error) {
        setLeaseTypes([]);
      }
    };
    fetchLeaseTypes();
  }, []);

  useEffect(() => {
    const fetchRentTypes = async () => {
      try {
        const result = await rentTypeAPI.getDropdown();
        if (Array.isArray(result)) {
          setRentTypes(result);
        } else if (result && result.success && Array.isArray(result.data)) {
          setRentTypes(result.data);
        } else {
          setRentTypes([]);
        }
      } catch (error) {
        setRentTypes([]);
      }
    };
    fetchRentTypes();
  }, []);

  const removeDocument = (index) => {
    setDocuments(prev => prev.filter((_, i) => i !== index));
    setDocumentFiles(prev => prev.filter((_, i) => i !== index));
  };

  const updateDocument = (index, field, value) => {
    setDocuments(prev => prev.map((doc, i) =>
      i === index ? { ...doc, [field]: value } : doc
    ));
  };

  const handleDocumentFileUpload = (index, file) => {
    setDocumentFiles(prev => {
      const newFiles = [...prev];
      newFiles[index] = file;
      return newFiles;
    });
    updateDocument(index, 'file', file);
  };
  // Dropdown state for property stages
  const [propertyStageOptions, setPropertyStageOptions] = useState([]);
  const [loadingStages, setLoadingStages] = useState(true);
  const [dropdownError, setDropdownError] = useState(null);
   

  // dropdown unitType
  const [unitTypeOptions, setUnitTypeOptions] = useState([]);
  const [loadingUnitTypes, setLoadingUnitTypes] = useState(true);
  const [unitTypeDropdownError, setUnitTypeDropdownError] = useState(null);
  const [unitTypeLoading, setUnitTypeLoading] = useState(false);
  
  // Fetch property stages for dropdown
  const fetchPropertyStages = async () => {
    setLoadingStages(true);
    setDropdownError(null);
    try {
      const response = await propertyStagesAPI.getDropdown();
      if (response.success) {
        setPropertyStageOptions(response.data || []);
      } else {
        setDropdownError(response.message || 'Failed to fetch property conditions');
      }
    } catch (err) {
      setDropdownError('Failed to fetch property conditions');
    } finally {
      setLoadingStages(false);
    }
  };

  useEffect(() => {
    fetchPropertyStages();
  }, []);

  // unit types dropdown 
  useEffect(() => {
  const fetchUnitTypes = async () => {
    try {
      const result = await unitTypeAPI.getDropdown(); // or getAll(), depending on your API
      if (result && result.success && Array.isArray(result.data)) {
        setUnitTypes(result.data);
      } else {
        setUnitTypes([]);
      }
    } catch (error) {
      setUnitTypes([]);
    }
  };

  if (isOpen) {
    fetchUnitTypes();
  }
}, [isOpen]);


  // Helper function to safely create object URL
  const safeCreateObjectURL = (file) => {
    try {
      // Check if it's a valid File or Blob object
      if (file && (file instanceof File || file instanceof Blob)) {
        return URL.createObjectURL(file);
      }
      // If it's not a valid file object, return null
      return null;
    } catch (error) {
      console.error('Error creating object URL:', error);
      return null;
    }
  };

  // Helper function to get image source safely
  const getImageSrc = (image) => {
    // If it has an image_url, use that
    if (image && image.image_url) {
      return image.image_url;
    }
    
    // If it has an image_path, construct the full URL
    if (image && image.image_path) {
      return `/storage/${image.image_path}`;
    }
    
    // Try to create object URL for File/Blob objects
    const objectUrl = safeCreateObjectURL(image);
    if (objectUrl) {
      return objectUrl;
    }
    
    // Return null for fallback handling
    return null;
  };

  // Helper function to check if image is valid
  const isValidImage = (image) => {
    // Check if image object exists and has required properties
    if (!image) return false;
    
    // Check if it's a valid file object (newly uploaded)
    if (image instanceof File || image instanceof Blob) {
      return true;
    }
    
    // For database images, perform strict validation
    if (image.image_path && typeof image.image_path === 'string') {
      // Check if it has all required database properties
      if (!image.image_name || !image.id || !image.project_id) {
        return false;
      }
      
      // Check if the path looks valid (not empty, has proper format)
      if (image.image_path.trim() === '' || !image.image_path.includes('/')) {
        return false;
      }
      
      // Check if image_name looks valid (has extension)
      if (!image.image_name.includes('.')) {
        return false;
      }
      
      // Additional check: ensure the path ends with a valid image extension
      const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
      const pathLower = image.image_path.toLowerCase();
      const hasValidExtension = validExtensions.some(ext => pathLower.endsWith(ext));
      
      if (!hasValidExtension) {
        return false;
      }
      
      return true;
    }
    
    // Check if it has a valid image_url
    if (image.image_url && typeof image.image_url === 'string' && image.image_url.trim() !== '') {
      return true;
    }
    
    return false;
  };

  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('property');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    property_type_id: '', // Changed from property_type to property_type_id
    property_status_id: '', // New field for property status
  property_stage_id:'',
    location: '',
    address: '',
    location_id: '',
    state_id: '',
    country_id: '',
    postal_code: '',
    latitude: '',
    longitude: '',
    area_sqft: '',
    price_per_area_sqft: '',
    total_price: '',
    bedrooms: '',
    bathrooms: '',
    floors: '',
    parking_spaces: '',
    year_built: '',
    amenities: [],
    features: [],
    is_featured: false,
    is_available: true
  });
  
  const [units, setUnits] = useState([]);
  const [images, setImages] = useState([]);
  const [videos, setVideos] = useState([]);
  const [imageFiles, setImageFiles] = useState([]);
  const [videoFiles, setVideoFiles] = useState([]);
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [locations, setLocations] = useState([]);
  const [propertyTypes, setPropertyTypes] = useState([]);
  const [propertyStatuses, setPropertyStatuses] = useState([]);
  const [unitTypes, setUnitTypes] = useState([]); // Dynamic unit types
  const [modifyPriceStates, setModifyPriceStates] = useState({}); // Track which units allow price modification

   const propertyStage = {
    new: 'New',
    old: 'Old'
  };


  

  const unitStatuses = {
    available: 'Available',
    rented: 'Rented',
    sold: 'Sold',
    leased: 'Leased',
    maintenance: 'Under Maintenance',
    reserved: 'Reserved'
  };

  const imageTypes = {
    exterior: 'Exterior',
    interior: 'Interior',
    floor_plan: 'Floor Plan',
    amenity: 'Amenity',
    view: 'View',
    gallery: 'Gallery',
    construction: 'Construction Progress'
  };

  const propertyService = {
    rent: 'For Rent',
    sale: 'For Sale',
    lease: 'For Lease',
    
  };

  // Initialize form data when project changes
  useEffect(() => {
    if (project && (mode === 'edit' || mode === 'view')) {
     
      setFormData({
        title: project.title || '',
        description: project.description || '',
        property_type_id: project.property_type_id || '', // Changed to property_type_id
        property_status_id: project.property_status_id || '', // New field for property status
  property_stage_id: project.property_stage_id ? project.property_stage_id.toString() : '',
        location: project.location || '',
        address: project.address || '',
        location_id: project.location_id ? project.location_id.toString() : '',
        state_id: project.state_id ? project.state_id.toString() : '',
        country_id: project.country_id ? project.country_id.toString() : (project.country?.id ? project.country.id.toString() : ''),
        postal_code: project.postal_code || '',
        latitude: project.latitude || '',
        longitude: project.longitude || '',
        area_sqft: project.area_sqft || '',
        price_per_area_sqft: project.price_per_area_sqft || '',
        total_price:project.total_price,
        bedrooms: project.bedrooms || '',
        bathrooms: project.bathrooms || '',
        floors: project.floors || '',
        parking_spaces: project.parking_spaces || '',
        year_built: project.year_built || '',
        amenities: project.amenities || [],
        features: project.features || [],
        is_featured: project.is_featured || false,
        is_available: project.is_available !== undefined ? project.is_available : true
      });
      setUnits(project.units || []);
      setImages(project.images || []);
      setVideos(project.videos || []);
      
      // Load states and locations for edit mode
      if (project.country_id) {
        loadStatesForCountry(project.country_id);
      }
      if (project.state_id) {
        loadLocationsForState(project.state_id);
      }
    } else {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        property_type_id: '', // Changed to property_type_id
        property_status_id: '', // New field for property status
  property_stage_id: '',
        location: '',
        address: '',
        location_id: '',
        state_id: '',
        country_id: '',
        postal_code: '',
        latitude: '',
        longitude: '',
        area_sqft: '',
       price_per_area_sqft: '',
       total_price:'',
        bedrooms: '',
        bathrooms: '',
        floors: '',
        parking_spaces: '',
        year_built: '',
        amenities: [],
        features: [],
        is_featured: false,
        is_available: true
      });
      setUnits([]);
      setImages([]);
      setVideos([]);
      setImageFiles([]);
      setVideoFiles([]);
      setStates([]);
      setLocations([]);
    }
  }, [project, mode]);

  // Helper functions to load dependent data
  const loadStatesForCountry = async (countryId) => {
    if (!countryId || countryId === 'NoCountrySelected') {
      setStates([]);
      return;
    }

    try {
      const result = await stateAPI.getStatesByCountry(countryId, { per_page: 250 });
      if (result && result.success && Array.isArray(result.data)) {
        setStates(result.data);
      } else {
        setStates([]);
      }
    } catch (error) {
      console.error('Error fetching states:', error);
      setStates([]);
    }
  };

  const loadLocationsForState = async (stateId) => {
    if (!stateId || stateId === 'NoStateSelected') {
      setLocations([]);
      return;
    }

    try {
      const result = await locationAPI.public.getLocationsByState(stateId, { per_page: 250 });
      if (result && result.success && Array.isArray(result.data)) {
        setLocations(result.data);
      } else {
        setLocations([]);
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
      setLocations([]);
    }
  };

  // Cleanup object URLs on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      // Cleanup any object URLs created for image files
      imageFiles.forEach(file => {
        if (file instanceof File || file instanceof Blob) {
          try {
            URL.revokeObjectURL(file);
          } catch (error) {
            // Ignore errors during cleanup
          }
        }
      });
      
      // Cleanup any object URLs created for video files
      videoFiles.forEach(file => {
        if (file instanceof File || file instanceof Blob) {
          try {
            URL.revokeObjectURL(file);
          } catch (error) {
            // Ignore errors during cleanup
          }
        }
      });
    };
  }, [imageFiles, videoFiles]);

  // Fetch property types for dropdown
  useEffect(() => {
    const fetchPropertyTypes = async () => {
      try {
        const result = await propertyTypeAPI.getAll({ status: 'active', per_page: 250 });
        
        if (result && result.success && Array.isArray(result.data.data)) {
          setPropertyTypes(result.data.data);
        } else {
          setPropertyTypes([]);
        }
      } catch (error) {
        console.error('Error fetching property types:', error);
        setPropertyTypes([]);
      }
    };

    const fetchPropertyStatuses = async () => {
      try {
        const result = await propertyStatusAPI.getDropdown();
        
        if (result && result.success && Array.isArray(result.data)) {
          setPropertyStatuses(result.data);
        } else {
          setPropertyStatuses([]);
        }
      } catch (error) {
        console.error('Error fetching property statuses:', error);
        setPropertyStatuses([]);
      }
    };

    if (isOpen) {
      fetchPropertyTypes();
      fetchPropertyStatuses();
    }
  }, [isOpen]);

  // fetch proerty conditons
  useEffect(() => {
    const fetchPropertyStage = async () => {
      try {
        const result = await propertyStagesAPI.getDropdown();
        
        if (result && result.success && Array.isArray(result.data)) {
          setPropertyStageOptions(result.data);
        } else {
          setPropertyStageOptions([]);
        }
      } catch (error) {
        console.error('Error fetching property stages:', error);
        setPropertyStageOptions([]);
      }
    };
    if (isOpen) {
      fetchPropertyStage();
    }
  }, [isOpen]);


  // Fetch countries for dropdown
  useEffect(() => {
    const fetchCountries = async () => {
      try {
       
        const result = await countryAPI.getCountries({ per_page: 250 }); // Get all countries
    

        if (result && result.success && Array.isArray(result.data)) {
          setCountries(result.data);
          
        } else {
        
          setCountries([]);
        }
      } catch (error) {
        console.error('Error fetching countries:', error);
        setCountries([]);
      }
    };

    if (isOpen) {
      fetchCountries();
    }
  }, [isOpen]);

  // Fetch states when country changes
  useEffect(() => {
    const fetchStates = async (countryId) => {
      if (!countryId || countryId === 'NoCountrySelected') {
        setStates([]);
        return;
      }

      try {
        const result = await stateAPI.getStatesByCountry(countryId, { per_page: 250 });
        if (result && result.success && Array.isArray(result.data)) {
          setStates(result.data);
        } else {
          setStates([]);
        }
      } catch (error) {
        console.error('Error fetching states:', error);
        setStates([]);
      }
    };

    if (formData.country_id && formData.country_id !== 'NoCountrySelected') {
      fetchStates(formData.country_id);
      // Only reset state selection when country changes in create mode or when manually changing country
      if (mode === 'create' || (project && project.country_id !== parseInt(formData.country_id))) {
        setFormData(prev => ({
          ...prev,
          state_id: '',
          location_id: ''
        }));
      }
    } else {
      setStates([]);
      if (mode === 'create') {
        setFormData(prev => ({
          ...prev,
          state_id: '',
          location_id: ''
        }));
      }
    }
  }, [formData.country_id, mode]);

  // Fetch locations when state changes
  useEffect(() => {
    const fetchLocations = async (stateId) => {
      if (!stateId || stateId === 'NoStateSelected') {
        setLocations([]);
        return;
      }

      try {
        const result = await locationAPI.public.getLocationsByState(stateId, { per_page: 250 });

        if (result && result.success && Array.isArray(result.data)) {
          setLocations(result.data);
        } else {
          setLocations([]);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
        setLocations([]);
      }
    };

    if (formData.state_id && formData.state_id !== 'NoStateSelected') {
      fetchLocations(formData.state_id);
      // Only reset location selection when state changes in create mode or when manually changing state
      if (mode === 'create' || (project && project.state_id !== parseInt(formData.state_id))) {
        setFormData(prev => ({
          ...prev,
          location_id: ''
        }));
      }
    } else {
      setLocations([]);
      if (mode === 'create') {
        setFormData(prev => ({
          ...prev,
          location_id: ''
        }));
      }
    }
  }, [formData.state_id, mode]);

  // Helper function to get property type name by id
  const getPropertyTypeName = (propertyTypeId) => {
    const propertyType = propertyTypes.find(pt => pt.id.toString() === propertyTypeId?.toString());
    return propertyType ? propertyType.name : 'Property Type';
  };

  // Helper function to get property status name by id
  const getPropertyStatusName = (propertyStatusId) => {
    const propertyStatus = propertyStatuses.find(ps => ps.id.toString() === propertyStatusId?.toString());
    return propertyStatus ? propertyStatus.name : 'Property Status';
  };

  // Helper function to get property status color by id
  const getPropertyStatusColor = (propertyStatusId) => {
    const propertyStatus = propertyStatuses.find(ps => ps.id.toString() === propertyStatusId?.toString());
    return propertyStatus ? propertyStatus.color : '#6B7280';
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle location dropdown change and auto-set location field
  const handleLocationChange = (locationId) => {
    const selectedLocation = locations.find(loc => loc.id === parseInt(locationId));
    setFormData(prev => ({
      ...prev,
      location_id: locationId,
      location: selectedLocation ? selectedLocation.name : ''
    }));
  };

  // Auto-calculate total price when area or price per sq ft changes
  useEffect(() => {
    // Don't auto-calculate during initial project load in edit mode
    if (mode === 'edit' && project && formData.area_sqft === project.area_sqft && formData.price_per_area_sqft === project.price_per_area_sqft && formData.total_price === project.total_price) {
      return;
    }

    const area = parseFloat(formData.area_sqft) || 0;
    const pricePerSqft = parseFloat(formData.price_per_area_sqft) || 0;
    
    if (area > 0 && pricePerSqft > 0) {
      const totalPrice = area * pricePerSqft;
      setFormData(prev => ({
        ...prev,
        total_price: totalPrice.toFixed(2)
      }));
    } else if (area === 0 || pricePerSqft === 0) {
      // Clear total price if either value is 0 or empty
      setFormData(prev => ({
        ...prev,
        total_price: ''
      }));
    }
  }, [formData.area_sqft, formData.price_per_area_sqft, mode, project]);

  // Recalculate unit prices when property price per sqft changes
  useEffect(() => {
    if (formData.price_per_area_sqft && units.length > 0) {
      units.forEach((unit, index) => {
        if (unit.unit_size && unit.propertyService && !modifyPriceStates[`${index}_rent`] && !modifyPriceStates[`${index}_sell`] && !modifyPriceStates[`${index}_lease`]) {
          calculateUnitPrice(index, unit.unit_size, unit.propertyService);
        }
      });
    }
  }, [formData.price_per_area_sqft]);

  // Unit management functions
  const addUnit = () => {
    const newUnit = {
      id: Date.now(),
      unit_number: '',
      unit_size: '',
  unit_type_id: '',
      propertyService:'',
      floor_number: '',
      area_sqft: '',
      bedrooms: '',
      bathrooms: '',
      rent_price: '',
      sell_price: '',
      lease_price: '',
      currency: 'USD',
      status: 'available',
      description: '',
      features: [],
      is_available: true
    };
    
    setUnits(prev => [newUnit, ...prev]);
    
    // Optional: Set active tab to units if not already
    if (activeTab !== 'units') {
      setActiveTab('units');
    }
  };

  const removeUnit = (index) => {
    setUnits(prev => prev.filter((_, i) => i !== index));
  };

  const updateUnit = (index, field, value) => {
    setUnits(prev => prev.map((unit, i) => 
      i === index ? { ...unit, [field]: value } : unit
    ));

    // Auto-calculate price when unit size or property service changes
    if (field === 'unit_size' || field === 'propertyService') {
      calculateUnitPrice(index, field === 'unit_size' ? value : units[index]?.unit_size, field === 'propertyService' ? value : units[index]?.propertyService);
    }
  };

  // Calculate unit price based on size and property per sqft price
  const calculateUnitPrice = (unitIndex, unitSize, propertyService) => {
    const size = parseFloat(unitSize) || 0;
    const pricePerSqft = parseFloat(formData.price_per_area_sqft) || 0;
    
    if (size > 0 && pricePerSqft > 0 && propertyService) {
      const calculatedPrice = (size * pricePerSqft).toFixed(2);
      
      // Update the appropriate price field based on propertyService
      setUnits(prev => prev.map((unit, i) => {
        if (i === unitIndex) {
          const updatedUnit = { ...unit };
          
          // Reset all price fields first
          updatedUnit.rent_price = '';
          updatedUnit.sell_price = '';
          updatedUnit.lease_price = '';
          
          // Set the relevant price field
          switch (propertyService) {
            case 'rent':
              updatedUnit.rent_price = calculatedPrice;
              break;
            case 'sale':
              updatedUnit.sell_price = calculatedPrice;
              break;
            case 'lease':
              updatedUnit.lease_price = calculatedPrice;
              break;
            case 'both_rent_sale':
              updatedUnit.rent_price = calculatedPrice;
              updatedUnit.sell_price = calculatedPrice;
              break;
            case 'both_lease_sale':
              updatedUnit.lease_price = calculatedPrice;
              updatedUnit.sell_price = calculatedPrice;
              break;
            case 'all':
              updatedUnit.rent_price = calculatedPrice;
              updatedUnit.sell_price = calculatedPrice;
              updatedUnit.lease_price = calculatedPrice;
              break;
            default:
              break;
          }
          
          return updatedUnit;
        }
        return unit;
      }));

      // Reset modify price state for this unit when auto-calculating
      setModifyPriceStates(prev => ({
        ...prev,
        [`${unitIndex}_rent`]: false,
        [`${unitIndex}_sell`]: false,
        [`${unitIndex}_lease`]: false
      }));
    }
  };

  // Toggle modify price state for a specific unit and price type
  const toggleModifyPrice = (unitIndex, priceType) => {
    const key = `${unitIndex}_${priceType}`;
    setModifyPriceStates(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Check if a price field should be shown based on propertyService
  const shouldShowPriceField = (propertyService, priceType) => {
    if (!propertyService) return false;
    
    switch (propertyService) {
      case 'rent':
        return priceType === 'rent';
      case 'sale':
        return priceType === 'sell';
      case 'lease':
        return priceType === 'lease';
      case 'both_rent_sale':
        return priceType === 'rent' || priceType === 'sell';
      case 'both_lease_sale':
        return priceType === 'lease' || priceType === 'sell';
      case 'all':
        return true;
      default:
        return false;
    }
  };

  // Check if price field should be disabled (when auto-calculated and not in modify mode)
  const isPriceFieldDisabled = (unitIndex, priceType, isReadOnly) => {
    if (isReadOnly) return true;
    const modifyKey = `${unitIndex}_${priceType}`;
    return !modifyPriceStates[modifyKey];
  };

  // Image management functions
  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);
    setImageFiles(prev => [...prev, ...files]);
  };

  const removeImageFile = (index) => {
    setImageFiles(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingImage = async (imageId) => {
    try {
      if (projectData?.id) {
        // Make API call to permanently delete the image from database and storage
        const response = await fetch(`/api/projects/${projectData.id}/images/${imageId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            'Content-Type': 'application/json',
          }
        });

        const result = await response.json();
        
        if (!result.success) {
          showAlert('error', 'Error', result.message || 'Failed to delete image');
          return;
        }

        showAlert('success', 'Success', 'Image deleted permanently from database');
      }
      
      // Remove from frontend state
      setImages(prev => prev.filter(img => img.id !== imageId));
    } catch (error) {
      console.error('Error deleting image:', error);
      showAlert('error', 'Error', 'Failed to delete image. Please try again.');
    }
  };


  // Video management functions (Repeater)
  const addVideo = () => {
    setVideos(prev => [
      {
        id: Date.now(),
        video_type: 'youtube',
        title: '',
        description: '',
        youtube_url: '',
        video_url: '',
        is_featured: false
      },
      ...prev
    ]);
  };

  const removeVideo = (index) => {
    setVideos(prev => prev.filter((_, i) => i !== index));
  };

  const updateVideo = (index, field, value) => {
    setVideos(prev => prev.map((video, i) =>
      i === index ? { ...video, [field]: value } : video
    ));
  };

  const handleVideoFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setVideoFiles(prev => [...prev, ...files]);
  };

  const removeVideoFile = (index) => {
    setVideoFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Submit form
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (mode === 'view') return;

    setLoading(true);
    try {
      const submitData = new FormData();
      
      // Add form data
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== undefined && formData[key] !== '') {
          if (Array.isArray(formData[key])) {
            submitData.append(key, JSON.stringify(formData[key]));
          } else {
            submitData.append(key, formData[key].toString());
          }
        }
      });


     
      
      for (let [key, value] of submitData.entries()) {
      
      }

      // Add units data
      if (units.length > 0) {
        submitData.append('units', JSON.stringify(units));
      }

      // Add image files
      imageFiles.forEach((file, index) => {
        submitData.append(`images[${index}]`, file);
      });

      // Add video data
      if (videos.length > 0) {
        submitData.append('videos', JSON.stringify(videos));
      }

      // Add video files
      videoFiles.forEach((file, index) => {
        submitData.append(`video_files[${index}]`, file);
      });

      // Add method spoofing for Laravel PUT requests in edit mode
      if (mode === 'edit') {
        submitData.append('_method', 'PUT');
      }

      let response;
      if (mode === 'create') {
        response = await projectAPI.create(submitData);
      } else {
        response = await projectAPI.update(project.id, submitData);
      }

      await showAlert('success', 'Success', `Project ${mode === 'create' ? 'created' : 'updated'} successfully!`);

      onSuccess();
    } catch (error) {
      console.error('Error saving project:', error);
      await showAlert('error', 'Error', error.response?.data?.message || `Failed to ${mode} project`);
    } finally {
      setLoading(false);
    }
  };

  const isReadOnly = mode === 'view';

  return isOpen ? (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
        {/* Modal Header with Gradient Background */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <Building2 className="h-6 w-6" />
            <div>
              <h3 className="text-lg font-semibold">
                {mode === 'create' ? 'Add New Property' : 
                 mode === 'edit' ? 'Edit Property' : 'View Property'}
              </h3>
              <p className="text-blue-100 text-sm">
                {mode === 'create' ? 'Create a new property listing' :
                 mode === 'edit' ? 'Update property information' : 'View property details'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <form onSubmit={handleSubmit} className="flex-1 flex flex-col overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col overflow-hidden">
              {/* Sticky Tab Header */}
              <div className="flex-shrink-0 sticky top-0 z-10 bg-white border-b border-gray-200 px-6 py-4">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="property">Property</TabsTrigger>
                  <TabsTrigger value="units">Units</TabsTrigger>
                  <TabsTrigger value="images">Images</TabsTrigger>
                  <TabsTrigger value="videos">Videos</TabsTrigger>
                  <TabsTrigger value="documents">Documents</TabsTrigger>
                </TabsList>
              </div>
              
              {/* Scrollable Tab Content */}
              <div className="flex-1 overflow-y-auto px-6 py-4 min-h-0">

            {/* Property Tab */}
            <TabsContent value="property" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Property Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter property title"
                    required
                    disabled={isReadOnly}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="property_type_id">Property Type *</Label>
                  <Select 
                    value={formData.property_type_id} 
                    onValueChange={(value) => handleInputChange('property_type_id', value)}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select property type" />
                    </SelectTrigger>
                    <SelectContent>
                      {propertyTypes.map((propertyType) => (
                        <SelectItem key={propertyType.id} value={propertyType.id.toString()}>
                          {propertyType.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="property_status_id">Property Status *</Label>
                  <Select 
                    value={formData.property_status_id} 
                    onValueChange={(value) => handleInputChange('property_status_id', value)}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select property status" />
                    </SelectTrigger>
                    <SelectContent>
                      {propertyStatuses.map((propertyStatus) => (
                        <SelectItem key={propertyStatus.id} value={propertyStatus.id.toString()}>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-3 h-3 rounded-full" 
                              style={{ backgroundColor: propertyStatus.color }}
                            ></div>
                            {propertyStatus.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="property_stage_id">Property Stage *</Label>
                  <Select 
                    value={formData.property_stage_id} 
                    onValueChange={(value) => handleInputChange('property_stage_id', value)}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select property stage" />
                    </SelectTrigger>
                   <SelectContent>
                      {propertyStageOptions.length === 0 && !loadingStages && (
                        <div className="px-4 py-2 text-gray-500">No Stages found</div>
                      )}
                      {propertyStageOptions.map((stage) => (
                        <SelectItem key={stage.id} value={stage.id.toString()}>
                          {stage.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

               
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <RichTextEditor
                  value={formData.description}
                  onChange={(value) => handleInputChange('description', value)}
                  placeholder="Enter property description"
                  disabled={isReadOnly}
                  height="200px"
                />
                </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="country_id">Country</Label>
                    <Select
                    value={formData.country_id}
                    onValueChange={(value) => {
                 
                      handleInputChange('country_id', value);
                    }}
                    disabled={isReadOnly}
                    >
                    <SelectTrigger>
                        <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="NoCountrySelected">No country selected</SelectItem>
                        {countries.map(country => (
                        <SelectItem key={country.id} value={country.id.toString()}>
                            {country.name}
                        </SelectItem>
                        ))}
                    </SelectContent>
                    </Select>
                   
                </div>

                <div className="space-y-2">
                    <Label htmlFor="state_id">State/Province *</Label>
                    <Select
                      value={formData.state_id}
                      onValueChange={(value) => handleInputChange('state_id', value)}
                      disabled={isReadOnly || !formData.country_id || formData.country_id === 'NoCountrySelected'}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={
                          !formData.country_id || formData.country_id === 'NoCountrySelected' 
                            ? "Please select a country first" 
                            : "Select state/province"
                        } />
                      </SelectTrigger>
                      <SelectContent>
                        {states.map(state => (
                          <SelectItem key={state.id} value={state.id.toString()}>
                            {state.name}
                          </SelectItem>
                        ))}
                        {states.length === 0 && formData.country_id && formData.country_id !== 'NoCountrySelected' && (
                          <SelectItem value="Not Selected" disabled>No states available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location_id">Location *</Label>
                  <Select
                    value={formData.location_id}
                    onValueChange={handleLocationChange}
                    disabled={isReadOnly || !formData.state_id || formData.state_id === 'NoStateSelected'}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={
                        !formData.state_id || formData.state_id === 'NoStateSelected' 
                          ? "Please select a state first" 
                          : "Select location"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {locations.map(location => (
                        <SelectItem key={location.id} value={location.id.toString()}>
                          {location.name}
                        </SelectItem>
                      ))}
                      {locations.length === 0 && formData.state_id && formData.state_id !== 'NoStateSelected' && (
                        <SelectItem value="Not Selected" disabled>No locations available</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {/* Debug info for development */}
                 
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="address">Address *</Label>
                  <RichTextEditor
                    id="address"
                    value={formData.address}
                    onChange={(value) => handleInputChange('address', value)}
                    placeholder="Enter full address"
                    rows={2}
                    required
                    disabled={isReadOnly}
                  />
                </div>
              
               
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="area_sqft">Area (sq ft)</Label>
                  <Input
                    id="area_sqft"
                    type="number"
                    value={formData.area_sqft}
                    onChange={(e) => handleInputChange('area_sqft', e.target.value)}
                    placeholder="Area"
                    disabled={isReadOnly}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="price_per_area_sqft">Price (sq ft)</Label>
                  <Input
                    id="price_per_area_sqft"
                    type="number"
                    value={formData.price_per_area_sqft}
                    onChange={(e) => handleInputChange('price_per_area_sqft', e.target.value)}
                    placeholder="Price Per Square Feet"
                    disabled={isReadOnly}
                  />
                </div>
                 <div className="space-y-2">
                  <Label htmlFor="total_price">Total Price (Auto-calculated)</Label>
                  <Input
                    id="total_price"
                    type="number"
                    value={formData.total_price}
                    onChange={(e) => handleInputChange('total_price', e.target.value)}
                    placeholder="Total Price"
                    disabled={isReadOnly}
                    className="bg-blue-50 border-blue-200"
                  />
                 
                </div>
              </div>
            <h3>For frontend View</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                
                 <div className="space-y-2">
                  <Label htmlFor="floors">Floors</Label>
                  <Input
                    id="floors"
                    type="number"
                    value={formData.floors}
                    onChange={(e) => handleInputChange('floors', e.target.value)}
                    placeholder="Floors"
                    disabled={isReadOnly}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bedrooms">Bedrooms</Label>
                  <Input
                    id="bedrooms"
                    type="number"
                    value={formData.bedrooms}
                    onChange={(e) => handleInputChange('bedrooms', e.target.value)}
                    placeholder="Number of bedrooms"
                    disabled={isReadOnly}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bathrooms">Bathrooms</Label>
                  <Input
                    id="bathrooms"
                    type="number"
                    value={formData.bathrooms}
                    onChange={(e) => handleInputChange('bathrooms', e.target.value)}
                    placeholder="Number of bathrooms"
                    disabled={isReadOnly}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4 pb-[10px]">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_featured"
                    checked={formData.is_featured}
                    onCheckedChange={(checked) => handleInputChange('is_featured', checked)}
                    disabled={isReadOnly}
                  />
                  <Label htmlFor="is_featured">Featured Property</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_available"
                    checked={formData.is_available}
                    onCheckedChange={(checked) => handleInputChange('is_available', checked)}
                    disabled={isReadOnly}
                  />
                  <Label htmlFor="is_available">Available</Label>
                </div>
              </div>
            </TabsContent>

            {/* Units Tab */}
            <TabsContent value="units" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Unit Management</h3>
                {!isReadOnly && (
                  <Button type="button" onClick={addUnit} variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Unit
                  </Button>
                )}
              </div>

              <div className="space-y-4">
                {units.map((unit, index) => (
                  <Card key={unit.id || index} className="border-gray-200 shadow-sm">
                    <CardHeader className="bg-gray-50 border-b pb-3">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-sm flex items-center">
                          <Building2 className="h-4 w-4 mr-2 text-blue-600" />
                          Unit {index + 1}
                        </CardTitle>
                        {!isReadOnly && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeUnit(index)}
                            className="text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4 p-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Unit Number *</Label>
                          <Input
                            value={unit.unit_number}
                            onChange={(e) => updateUnit(index, 'unit_number', e.target.value)}
                            placeholder="e.g., A-101"
                            disabled={isReadOnly}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Unit Type *</Label>
                          <Select
                            value={unit.unit_type_id || ''}
                            onValueChange={(value) => updateUnit(index, 'unit_type_id', value)}
                            disabled={isReadOnly}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select unit type" />
                            </SelectTrigger>
                            <SelectContent>
                              {unitTypes.map((unitType) => (
                                <SelectItem key={unitType.id} value={unitType.id.toString()}>
                                  {unitType.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Unit size *</Label>
                          <Input
                            value={unit.unit_size}
                            onChange={(e) => updateUnit(index, 'unit_size', e.target.value)}
                            placeholder="e.g., A-101"
                            disabled={isReadOnly}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Floor</Label>
                          <Input
                            type="number"
                            value={unit.floor_number}
                            onChange={(e) => updateUnit(index, 'floor_number', e.target.value)}
                            placeholder="Floor"
                            disabled={isReadOnly}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Property Service *</Label>
                          <Select 
                            value={unit.propertyService} 
                            onValueChange={(value) => updateUnit(index, 'propertyService', value)}
                            disabled={isReadOnly}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              {Object.entries(propertyService).map(([key, value]) => (
                                <SelectItem key={key} value={key}>{value}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Rent Price Field - Show only when propertyService includes rent */}
                        {shouldShowPriceField(unit.propertyService, 'rent') && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label>Rent Price ($)</Label>
                              {unit.unit_size && formData.price_per_area_sqft && (
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`modify-rent-${index}`}
                                    checked={modifyPriceStates[`${index}_rent`] || false}
                                    onCheckedChange={() => toggleModifyPrice(index, 'rent')}
                                    disabled={isReadOnly}
                                  />
                                  <Label 
                                    htmlFor={`modify-rent-${index}`} 
                                    className="text-xs text-gray-600"
                                  >
                                    Modify Price
                                  </Label>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center space-x-2">
                              <Input
                                type="number"
                                step="0.01"
                                value={unit.rent_price}
                                onChange={(e) => updateUnit(index, 'rent_price', e.target.value)}
                                placeholder="0.00"
                                disabled={isPriceFieldDisabled(index, 'rent', isReadOnly)}
                                className={!modifyPriceStates[`${index}_rent`] && unit.rent_price ? 'bg-blue-50 border-blue-200' : ''}
                              />
                              <Select
                                value={unit.rent_type_id || ''}
                                onValueChange={(value) => updateUnit(index, 'rent_type_id', value)}
                                disabled={isReadOnly}
                                className="min-w-[120px]"
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select rent type" />
                                </SelectTrigger>
                                <SelectContent>
                                  {rentTypes && rentTypes.length > 0 ? (
                                    rentTypes.map(rt => (
                                      <SelectItem key={rt.id} value={rt.id.toString()}>
                                        {rt.name}
                                      </SelectItem>
                                    ))
                                  ) : (
                                    <SelectItem value="" disabled>No rent types</SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          



                        )}

                        {/* Sell Price Field - Show only when propertyService includes sale */}
                        {shouldShowPriceField(unit.propertyService, 'sell') && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label>Sell Price ($)</Label>
                              {unit.unit_size && formData.price_per_area_sqft && (
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`modify-sell-${index}`}
                                    checked={modifyPriceStates[`${index}_sell`] || false}
                                    onCheckedChange={() => toggleModifyPrice(index, 'sell')}
                                    disabled={isReadOnly}
                                  />
                                  <Label 
                                    htmlFor={`modify-sell-${index}`} 
                                    className="text-xs text-gray-600"
                                  >
                                    Modify Price
                                  </Label>
                                </div>
                              )}
                            </div>
                            <Input
                              type="number"
                              step="0.01"
                              value={unit.sell_price}
                              onChange={(e) => updateUnit(index, 'sell_price', e.target.value)}
                              placeholder="0.00"
                              disabled={isPriceFieldDisabled(index, 'sell', isReadOnly)}
                              className={!modifyPriceStates[`${index}_sell`] && unit.sell_price ? 'bg-blue-50 border-blue-200' : ''}
                            />
                          </div>
                        )}

                        {/* Lease Price Field - Show only when propertyService includes lease */}
                        {shouldShowPriceField(unit.propertyService, 'lease') && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label>Lease Price ($)</Label>
                              {unit.unit_size && formData.price_per_area_sqft && (
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`modify-lease-${index}`}
                                    checked={modifyPriceStates[`${index}_lease`] || false}
                                    onCheckedChange={() => toggleModifyPrice(index, 'lease')}
                                    disabled={isReadOnly}
                                  />
                                  <Label 
                                    htmlFor={`modify-lease-${index}`} 
                                    className="text-xs text-gray-600"
                                  >
                                    Modify Price
                                  </Label>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center space-x-2">
                              <Input
                                type="number"
                                step="0.01"
                                value={unit.lease_price}
                                onChange={(e) => updateUnit(index, 'lease_price', e.target.value)}
                                placeholder="0.00"
                                disabled={isPriceFieldDisabled(index, 'lease', isReadOnly)}
                                className={!modifyPriceStates[`${index}_lease`] && unit.lease_price ? 'bg-blue-50 border-blue-200' : ''}
                              />
                              <Select
                                value={unit.lease_type_id || ''}
                                onValueChange={(value) => updateUnit(index, 'lease_type_id', value)}
                                disabled={isReadOnly}
                                className="min-w-[120px]"
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Lease type" />
                                </SelectTrigger>
                                <SelectContent>
                                  {leaseTypes && leaseTypes.length > 0 ? (
                                    leaseTypes.map(lt => (
                                      <SelectItem key={lt.id} value={lt.id.toString()}>
                                        {lt.name}
                                      </SelectItem>
                                    ))
                                  ) : (
                                    <SelectItem value="" disabled>No lease types</SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}

                        {/* Message when no propertyService is selected */}
                        {!unit.propertyService && (
                          <div className="col-span-3 text-center py-4 text-gray-500 bg-gray-50 rounded border-2 border-dashed border-gray-300">
                            Please select a Property Service to see relevant price fields
                          </div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label>Description</Label>
                        <RichTextEditor
                          value={unit.description}
                          onChange={(value) => updateUnit(index, 'description', value)}
                          placeholder="Unit description"
                          disabled={isReadOnly}
                          height="120px"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {units.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No units added yet. Click "Add Unit" to get started.
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Images Tab */}
            <TabsContent value="images" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Property Images</h3>
                {!isReadOnly && (
                  <div className="space-x-2">
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <Button 
                      type="button" 
                      variant="outline"
                      onClick={() => document.getElementById('image-upload').click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Images
                    </Button>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Existing images - filter out invalid ones upfront */}
                {images.filter(isValidImage).map((image, index) => (
                  <Card key={image.id || index} className="card-container border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="relative">
                        <img
                          src={getImageSrc(image)}
                          alt={image.title || image.alt_text || `Image ${index + 1}`}
                          className="w-full h-32 object-cover rounded"
                          onError={(e) => {
                            // Remove the entire card when image fails to load
                            const card = e.target.closest('.card-container');
                            if (card) {
                              card.remove();
                            }
                          }}
                        />
                        {!isReadOnly && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                            onClick={() => removeExistingImage(image.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      {/* Image info removed - showing only the image */}
                    </CardContent>
                  </Card>
                ))}

                {/* New image files - only show if they can create valid object URLs */}
                {imageFiles.filter(file => safeCreateObjectURL(file)).map((file, index) => (
                  <Card key={`new-${index}`} className="border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="relative">
                        <img
                          src={safeCreateObjectURL(file)}
                          alt={`New image ${index + 1}`}
                          className="w-full h-32 object-cover rounded"
                          onError={(e) => {
                            // Remove the entire card when image fails to load
                            const card = e.target.closest('.border-gray-200');
                            if (card) {
                              card.remove();
                            }
                          }}
                        />
                        {!isReadOnly && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="absolute top-2 right-2"
                            onClick={() => removeImageFile(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      {/* File info removed - showing only the image */}
                    </CardContent>
                  </Card>
                ))}
              </div>

              {images.filter(isValidImage).length === 0 && imageFiles.filter(file => safeCreateObjectURL(file)).length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No images uploaded yet. Click "Upload Images" to add some.
                </div>
              )}
            </TabsContent>

            {/* Videos Tab */}
            <TabsContent value="videos" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Property Videos</h3>
                {!isReadOnly && (
                  <div className="space-x-2">
                    <Button type="button" onClick={addVideo} variant="outline">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Video Link
                    </Button>
                    <input
                      type="file"
                      accept="video/*"
                      multiple
                      onChange={handleVideoFileUpload}
                      className="hidden"
                      id="video-upload"
                    />
                    <Button 
                      type="button" 
                      variant="outline"
                      onClick={() => document.getElementById('video-upload').click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Videos
                    </Button>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                {/* Video links */}
                {videos.map((video, index) => (
                  <Card key={video.id || index} className="border-gray-200 shadow-sm">
                    <CardHeader className="bg-gray-50 border-b pb-3">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-sm flex items-center">
                          <Video className="h-4 w-4 mr-2 text-blue-600" />
                          Video {index + 1}
                        </CardTitle>
                        {!isReadOnly && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeVideo(index)}
                            className="text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4 p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Video Type</Label>
                          <Select 
                            value={video.video_type} 
                            onValueChange={(value) => updateVideo(index, 'video_type', value)}
                            disabled={isReadOnly}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="youtube">YouTube</SelectItem>
                              <SelectItem value="url">Video URL</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>Title *</Label>
                          <Input
                            value={video.title}
                            onChange={(e) => updateVideo(index, 'title', e.target.value)}
                            placeholder="Video title"
                            disabled={isReadOnly}
                          />
                        </div>
                      </div>

                      {video.video_type === 'youtube' && (
                        <div className="space-y-2">
                          <Label>YouTube URL</Label>
                          <Input
                            value={video.youtube_url}
                            onChange={(e) => updateVideo(index, 'youtube_url', e.target.value)}
                            placeholder="https://www.youtube.com/watch?v=..."
                            disabled={isReadOnly}
                          />
                        </div>
                      )}

                      {video.video_type === 'url' && (
                        <div className="space-y-2">
                          <Label>Video URL</Label>
                          <Input
                            value={video.video_url}
                            onChange={(e) => updateVideo(index, 'video_url', e.target.value)}
                            placeholder="https://example.com/video.mp4"
                            disabled={isReadOnly}
                          />
                        </div>
                      )}

                      <div className="space-y-2">
                        <Label>Description</Label>
                        <RichTextEditor
                          value={video.description}
                          onChange={(value) => updateVideo(index, 'description', value)}
                          placeholder="Video description"
                          disabled={isReadOnly}
                          height="120px"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {/* Video files */}
                {videoFiles.map((file, index) => (
                  <Card key={`video-file-${index}`} className="border-gray-200 shadow-sm">
                    <CardContent className="p-6">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-3">
                          <Video className="h-8 w-8 text-gray-400" />
                          <div>
                            <p className="text-sm font-medium">{file.name}</p>
                            <p className="text-xs text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        {!isReadOnly && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeVideoFile(index)}
                            className="text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {videos.length === 0 && videoFiles.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No videos added yet. Add video links or upload video files.
                </div>
              )}
            </TabsContent>

            {/* Documents Tab */}
            <TabsContent value="documents" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Project Documents</h3>
                {!isReadOnly && (
                  <Button type="button" onClick={addDocument} variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Document
                  </Button>
                )}
              </div>
              <div className="space-y-4">
                {documents.map((doc, index) => (
                  <Card key={doc.id || index} className="border-gray-200 shadow-sm">
                    <CardHeader className="bg-gray-50 border-b pb-3">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-sm flex items-center">
                          <ImageIcon className="h-4 w-4 mr-2 text-blue-600" />
                          Document {index + 1}
                        </CardTitle>
                        {!isReadOnly && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeDocument(index)}
                            className="text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4 p-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Title *</Label>
                          <Input
                            value={doc.title}
                            onChange={e => updateDocument(index, 'title', e.target.value)}
                            placeholder="Document title"
                            disabled={isReadOnly}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Document (Image or PDF)</Label>
                          <Input
                            type="file"
                            accept="image/*,application/pdf"
                            onChange={e => handleDocumentFileUpload(index, e.target.files[0])}
                            disabled={isReadOnly}
                          />
                          {doc.file && (
                            <span className="text-xs text-gray-500">{doc.file.name}</span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 mt-6">
                          <Checkbox
                            id={`show-on-frontend-${index}`}
                            checked={doc.show_on_frontend}
                            onCheckedChange={checked => updateDocument(index, 'show_on_frontend', checked)}
                            disabled={isReadOnly}
                          />
                          <Label htmlFor={`show-on-frontend-${index}`}>Show on frontend</Label>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              {documents.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No documents added yet. Click "Add Document" to get started.
                </div>
              )}
            </TabsContent>
            <TabsContent value="preview" className="space-y-4">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      {formData.title || 'Property Title'}
                    </CardTitle>
                    <div className="flex gap-2">
                      <Badge>{getPropertyTypeName(formData.property_type_id)}</Badge>
                      <Badge 
                        variant="outline" 
                        style={{ 
                          borderColor: getPropertyStatusColor(formData.property_status_id),
                          color: getPropertyStatusColor(formData.property_status_id)
                        }}
                      >
                        <div className="flex items-center gap-1">
                          <div 
                            className="w-2 h-2 rounded-full" 
                            style={{ backgroundColor: getPropertyStatusColor(formData.property_status_id) }}
                          ></div>
                          {getPropertyStatusName(formData.property_status_id)}
                        </div>
                      </Badge>
                       <Badge variant="outline">{propertyStage[formData.property_stage] || 'Property Stage'}</Badge>
                      {formData.is_featured && <Badge variant="secondary">Featured</Badge>}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-gray-700">{formData.description || 'No description provided'}</p>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <MapPin className="h-4 w-4" />
                      {formData.location || 'Location not specified'}
                    </div>
                  </CardContent>
                </Card>

                {units.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Units ({units.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-2">
                        {units.map((unit, index) => (
                          <div key={index} className="flex justify-between items-center p-2 border rounded">
                            <div>
                              <span className="font-medium">{unit.unit_number}</span>
                              <span className="text-sm text-gray-500 ml-2">
                                {unitTypes.find(ut => ut.id?.toString() === unit.unit_type_id?.toString())?.name || unit.unit_type_id}
                              </span>
                              <span className="text-sm text-gray-500 ml-2">{unit.propertyService}</span>
                              <span className="font-medium">{unit.unit_size}</span>
                            </div>
                            <div className="text-sm text-gray-600">
                              {unit.rent_price && `Rent: $${unit.rent_price}`}
                              {unit.sell_price && ` | Sell: $${unit.sell_price}`}
                              {unit.lease_price && ` | Lease: $${unit.lease_price}`}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {(images.filter(isValidImage).length > 0 || imageFiles.filter(file => safeCreateObjectURL(file)).length > 0) && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Images ({images.filter(isValidImage).length + imageFiles.filter(file => safeCreateObjectURL(file)).length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {images.filter(isValidImage).concat(imageFiles.filter(file => safeCreateObjectURL(file))).slice(0, 8).map((image, index) => {
                          const imageSrc = getImageSrc(image);
                          return (
                            <img
                              key={index}
                              src={imageSrc}
                              alt={`Preview ${index}`}
                              className="w-full h-20 object-cover rounded"
                              onError={(e) => {
                                // Hide broken preview images
                                e.target.style.display = 'none';
                              }}
                            />
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {(videos.length > 0 || videoFiles.length > 0) && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Videos ({videos.length + videoFiles.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {videos.map((video, index) => (
                          <div key={index} className="flex items-center gap-3 p-2 border rounded">
                            <Video className="h-5 w-5 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium">{video.title}</p>
                              <p className="text-xs text-gray-500">
                                {video.video_type === 'youtube' ? 'YouTube' : 'Video URL'}
                              </p>
                            </div>
                          </div>
                        ))}
                        {videoFiles.map((file, index) => (
                          <div key={`file-${index}`} className="flex items-center gap-3 p-2 border rounded">
                            <Video className="h-5 w-5 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium">{file.name}</p>
                              <p className="text-xs text-gray-500">
                                {(file.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>
            
            </div> {/* Close scrollable content div */}
            </Tabs>

            {/* Modal Actions - Fixed at bottom */}
            <div className="flex-shrink-0 flex justify-end space-x-2 p-6 pt-4 border-t bg-white">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
                disabled={loading}
                className="hover:bg-gray-50 transition-colors"
              >
                {mode === 'view' ? 'Close' : 'Cancel'}
              </Button>
              {mode !== 'view' && (
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  {mode === 'create' ? 'Create Property' : 'Update Property'}
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  ) : null;
};

export default ProjectModal;
