import React from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { ContentModal, ContentModalContent, ContentModalHeader, ContentModalTitle, ContentModalOverlay } from '../ui/content-modal';
import { Textarea } from '../ui/textarea';
import { FileText, MapPin, Upload, X } from 'lucide-react';

const ContractorModal = ({
  open,
  onOpenChange,
  title,
  formData,  
  setFormData,
  selectedFiles,
  setSelectedFiles,
  existingFiles = [],
  removeExistingFile,
  onSubmit,
  submitLabel,
  showStatus = true,
  showFiles = true
}) => {

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(prevFiles => [...prevFiles, ...files]);
  };

  const removeSelectedFile = (index) => {
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  return (
    <ContentModal open={open} onOpenChange={onOpenChange}>
      <ContentModalOverlay />
      <ContentModalContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <ContentModalHeader>
          <ContentModalTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            {title}
          </ContentModalTitle>
        </ContentModalHeader>
        <form onSubmit={onSubmit} className="space-y-4">
          <div className="grid gap-4 py-4">
            {/* Company Name & Trade License */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Company Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="Enter company name"
                  required
                />
              </div>
              <div>
                <Label htmlFor="trade_license">Trade License *</Label>
                <div className="relative">
                  <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="trade_license"
                    value={formData.trade_license}
                    onChange={(e) => setFormData({...formData, trade_license: e.target.value})}
                    placeholder="TL-XXX-YYYY"
                    className="pl-10"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Email & Phone */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  placeholder="+****************"
                  required
                />
              </div>
            </div>

            {/* Status */}
            {showStatus && (
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Address */}
            <div>
              <Label htmlFor="address">Business Address</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 text-gray-400 w-4 h-4" />
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData({...formData, address: e.target.value})}
                  placeholder="Enter complete business address"
                  className="pl-10"
                  rows={3}
                />
              </div>
            </div>

            {/* File Upload Section */}
            {showFiles && (
              <div className="space-y-3">
                <Label className="text-slate-700 font-medium">
                  <Upload className="w-4 h-4 inline mr-2" />
                  Documents
                </Label>

                {/* Existing Files */}
                {existingFiles.length > 0 && (
                  <div className="space-y-2">
                    {existingFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between bg-blue-50 p-2 rounded">
                        <span className="truncate flex-1">{file.name}</span>
                        <div className="flex items-center space-x-2">
                          <button type="button" onClick={() => window.open(file.url, '_blank')} className="text-blue-500 hover:text-blue-700">
                            <Upload className="w-4 h-4" />
                          </button>
                          <button type="button" onClick={() => removeExistingFile(index)} className="text-red-500 hover:text-red-700">
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                <div className="border-2 border-dashed border-slate-300 rounded-lg p-4 hover:border-slate-400 transition-colors">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileChange}
                    className="hidden"
                    id="files"
                  />
                  <label htmlFor="files" className="cursor-pointer flex flex-col items-center space-y-2 text-slate-600 hover:text-slate-800">
                    <Upload className="w-8 h-8" />
                    <span className="text-sm font-medium">Click to upload files</span>
                  </label>
                </div>

                {/* Selected Files */}
                {selectedFiles.length > 0 && (
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {selectedFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between bg-green-50 p-2 rounded">
                        <span className="truncate flex-1">{file.name}</span>
                        <span className="text-xs text-slate-500 mr-2">{(file.size / 1024 / 1024).toFixed(2)} MB</span>
                        <button type="button" onClick={() => removeSelectedFile(index)} className="text-red-500 hover:text-red-700">
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
            <Button type="submit">{submitLabel}</Button>
          </div>
        </form>
      </ContentModalContent>
    </ContentModal>
  );
};

export default ContractorModal;
