

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Search, Plus, Edit2, Trash2, Power, MoreHorizontal, Settings, AlertCircle, Loader2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import tenantsAPI from '../../services/tenantsAPI';
import { useAuth } from '../../contexts/AuthContext';
// Simple modal for Tenants

function TenantModal({ isOpen, onClose, onSave, tenant, mode }) {
  const isReadOnly = mode === 'view';
  const [formData, setFormData] = useState(
    tenant || {
      photo: null,
      first_name: '',
      last_name: '',
      phone: '',
      email: '',
      previous_address: '',
      permanent_address: '',
      documents: [],
      emergency_contact: { name: '', relation: '', phone: '' },
      note: '',
    }
  );
  const [photoPreview, setPhotoPreview] = useState(null);

  useEffect(() => {
    if (tenant && (mode === 'edit' || mode === 'view')) {
      setFormData({
        ...tenant,
        emergency_contact: tenant.emergency_contact || { name: '', relation: '', phone: '' },
        documents: tenant.documents || [],
      });
      setPhotoPreview(tenant.photo_url || null);
    } else if (mode === 'create') {
      setFormData({
        photo: null,
        first_name: '',
        last_name: '',
        phone: '',
        email: '',
        previous_address: '',
        permanent_address: '',
        documents: [],
        emergency_contact: { name: '', relation: '', phone: '' },
        note: '',
      });
      setPhotoPreview(null);
    }
  }, [tenant, mode]);

  // Document repeater handlers
  const handleDocumentChange = (idx, field, value) => {
    setFormData((prev) => {
      const docs = [...(prev.documents || [])];
      docs[idx] = { ...docs[idx], [field]: value };
      return { ...prev, documents: docs };
    });
  };
  const handleAddDocument = () => {
    setFormData((prev) => ({
      ...prev,
      documents: [...(prev.documents || []), { title: '', file: null }],
    }));
  };
  const handleRemoveDocument = (idx) => {
    setFormData((prev) => {
      const docs = [...(prev.documents || [])];
      docs.splice(idx, 1);
      return { ...prev, documents: docs };
    });
  };

  // Photo upload handler
  const handlePhotoChange = (e) => {
    const file = e.target.files[0];
    setFormData((prev) => ({ ...prev, photo: file }));
    if (file) {
      setPhotoPreview(URL.createObjectURL(file));
    } else {
      setPhotoPreview(null);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] flex flex-col overflow-hidden">
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <div>
              <h3 className="text-lg font-semibold">
                {mode === 'create' ? 'Add Tenant' : mode === 'edit' ? 'Edit Tenant' : 'View Tenant'}
              </h3>
              <p className="text-blue-100 text-sm">
                {mode === 'create' ? 'Create a new tenant record' : mode === 'edit' ? 'Update tenant information' : 'View tenant details'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            ×
          </button>
        </div>
        {/* Modal Content */}
        <div className="flex-1 flex flex-col overflow-y-auto px-6 py-4">
          <form
            onSubmit={e => {
              e.preventDefault();
              onSave(formData);
              onClose();
            }}
            className="space-y-6"
          >
            {/* Photo Upload */}
            <div className="flex items-center gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Photo</label>
                <div className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden mb-2">
                  {photoPreview ? (
                    <img src={photoPreview} alt="Preview" className="object-cover w-full h-full" />
                  ) : (
                    <span className="text-gray-400">No Photo</span>
                  )}
                </div>
                {!isReadOnly && (
                  <input type="file" accept="image/*" onChange={handlePhotoChange} />
                )}
              </div>
            </div>
            {/* Name Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">First Name</label>
                <Input
                  value={formData.first_name}
                  disabled={isReadOnly}
                  onChange={e => setFormData(prev => ({ ...prev, first_name: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Last Name</label>
                <Input
                  value={formData.last_name}
                  disabled={isReadOnly}
                  onChange={e => setFormData(prev => ({ ...prev, last_name: e.target.value }))}
                />
              </div>
            </div>
            {/* Contact Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Phone</label>
                <Input
                  value={formData.phone}
                  disabled={isReadOnly}
                  onChange={e => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <Input
                  type="email"
                  value={formData.email}
                  disabled={isReadOnly}
                  onChange={e => setFormData(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>
            </div>
            {/* Address Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Previous Address</label>
                <Input
                  value={formData.previous_address}
                  disabled={isReadOnly}
                  onChange={e => setFormData(prev => ({ ...prev, previous_address: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Permanent Address</label>
                <Input
                  value={formData.permanent_address}
                  disabled={isReadOnly}
                  onChange={e => setFormData(prev => ({ ...prev, permanent_address: e.target.value }))}
                />
              </div>
            </div>
            {/* Documents Repeater */}
            <div>
              <label className="block text-sm font-medium mb-1">Documents</label>
              {(formData.documents || []).map((doc, idx) => (
                <div key={idx} className="flex items-center gap-2 mb-2">
                  <Input
                    className="flex-1"
                    placeholder="Title"
                    value={doc.title || ''}
                    disabled={isReadOnly}
                    onChange={e => handleDocumentChange(idx, 'title', e.target.value)}
                  />
                  <input
                    type="file"
                    disabled={isReadOnly}
                    onChange={e => handleDocumentChange(idx, 'file', e.target.files[0])}
                  />
                  {!isReadOnly && (
                    <Button type="button" variant="destructive" onClick={() => handleRemoveDocument(idx)}>
                      Remove
                    </Button>
                  )}
                </div>
              ))}
              {!isReadOnly && (
                <Button type="button" variant="outline" onClick={handleAddDocument}>
                  Add Document
                </Button>
              )}
            </div>
            {/* Emergency Contact */}
            <div>
              <label className="block text-sm font-medium mb-1">Emergency Contact</label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                <Input
                  placeholder="Name"
                  value={formData.emergency_contact?.name || ''}
                  disabled={isReadOnly}
                  onChange={e => setFormData(prev => ({ ...prev, emergency_contact: { ...prev.emergency_contact, name: e.target.value } }))}
                />
                <Input
                  placeholder="Relation"
                  value={formData.emergency_contact?.relation || ''}
                  disabled={isReadOnly}
                  onChange={e => setFormData(prev => ({ ...prev, emergency_contact: { ...prev.emergency_contact, relation: e.target.value } }))}
                />
                <Input
                  placeholder="Contact Number"
                  value={formData.emergency_contact?.phone || ''}
                  disabled={isReadOnly}
                  onChange={e => setFormData(prev => ({ ...prev, emergency_contact: { ...prev.emergency_contact, phone: e.target.value } }))}
                />
              </div>
            </div>
            {/* Note */}
            <div>
              <label className="block text-sm font-medium mb-1">Note</label>
              <Input
                value={formData.note}
                disabled={isReadOnly}
                onChange={e => setFormData(prev => ({ ...prev, note: e.target.value }))}
              />
            </div>
            {/* Modal Actions */}
            <div className="flex-shrink-0 flex justify-end space-x-2 pt-4 border-t bg-white">
              <Button type="button" variant="outline" onClick={onClose}>
                {isReadOnly ? 'Close' : 'Cancel'}
              </Button>
              {!isReadOnly && (
                <Button type="submit">
                  Save
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function TenantPage() {
  const { hasPermission, isAuthenticated } = useAuth();

  // setup state
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');

  // Fetch tenants from backend (Read)
  const fetchTenants = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user has permission to read tenants
      if (!hasPermission('tanant', 'read')) {
        setError('You do not have permission to view tenants.');
        return;
      }

      const response = await tenantsAPI.getAll();

      // Handle the response structure properly
      if (response.success) {
        const data = response.data?.data || response.data || [];
        setTenants(Array.isArray(data) ? data : []);
      } else {
        setError(response.message || 'Failed to fetch tenants');
      }
    } catch (error) {
      console.error('Error fetching tenants:', error);
      if (error.status === 401) {
        setError('Authentication required. Please log in again.');
      } else if (error.status === 403) {
        setError('You do not have permission to view tenants.');
      } else {
        setError(error.message || 'Failed to fetch tenants. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchTenants();
    }
  }, [isAuthenticated]);



const handleCreate = async () => {
    if (!hasPermission('tanant', 'create')) {
      setError('You do not have permission to create tenants.');
      return;
    }
    setSelectedTenant(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleEdit = (tenant) => {
    if (!hasPermission('tanant', 'update')) {
      setError('You do not have permission to edit tenants.');
      return;
    }
    setSelectedTenant(tenant);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleView = (tenant) => {
    setSelectedTenant(tenant);
    setModalMode('view');
    setIsModalOpen(true);
  };

  const handleDelete = async (tenant) => {
    if (!hasPermission('tanant', 'delete')) {
      setError('You do not have permission to delete tenants.');
      return;
    }
    if (window.confirm(`Delete tenant "${tenant.name}"?`)) {
      try {
        const response = await tenantsAPI.delete(tenant.id);
        if (response.success) {
          fetchTenants(); // Refresh the data
          setError(null);
        } else {
          setError(response.message || 'Failed to delete tenant.');
        }
      } catch (error) {
        setError('Failed to delete tenant');
      }
    }
  };

  const handleToggleStatus = async (tenant) => {
    if (!hasPermission('tanant', 'update')) {
      setError('You do not have permission to update tenants.');
      return;
    }

    try {
      // Update status field instead of is_active
      const response = await tenantsAPI.update(tenant.id, {
        ...tenant,
        status: tenant.status === 'active' ? 'inactive' : 'active'
      });
      if (response.success) {
        fetchTenants(); // Refresh the data
        setError(null);
      } else {
        setError(response.message || 'Failed to update tenant status.');
      }
    } catch (error) {
      setError('Failed to update tenant status');
    }
  };

  function getImageUrl(photo) {
  if (!photo) {
    return '/default-avatar.png'; // Put this image in your React public folder
  }
  return `${process.env.APP_URL}/storage/${photo}`;
}
  // Create or Edit tenant (dynamic)
  const handleSave = async (formData) => {
    try {
      // Prepare FormData for file upload
      const data = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'documents' && Array.isArray(value)) {
          value.forEach((doc, i) => {
            data.append(`documents[${i}][title]`, doc.title || '');
            if (doc.file) data.append(`documents[${i}][file]`, doc.file);
          });
        } else if (key === 'emergency_contact' && value) {
          Object.entries(value).forEach(([ecKey, ecVal]) => {
            data.append(`emergency_contact[${ecKey}]`, ecVal || '');
          });
        } else if (key === 'photo' && value) {
          data.append('photo', value);
        } else if (value !== undefined && value !== null) {
          data.append(key, value);
        }
      });
      let response;
      if (modalMode === 'create') {
        response = await tenantsAPI.create(data, true);
      } else if (modalMode === 'edit') {
        response = await tenantsAPI.update(selectedTenant.id, data, true);
      }

      if (response.success) {
        fetchTenants(); // Refresh the data
        setIsModalOpen(false);
        setSelectedTenant(null);
        setError(null);
      } else {
        setError(response.message || 'Failed to save tenant.');
      }
    } catch (error) {
      setError('Failed to save tenant');
    }
  };

  const filteredTenants = tenants.filter((tenant) => {
    const matchesSearch =
      ((tenant.first_name && tenant.first_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (tenant.last_name && tenant.last_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (tenant.email && tenant.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (tenant.phone && tenant.phone.includes(searchTerm)));
    const matchesStatus =
      statusFilter === 'all' ||
  (statusFilter === 'active' && tenant.status === 'active') ||
  (statusFilter === 'inactive' && tenant.status === 'inactive');
    return matchesSearch && matchesStatus;
  });

  // Show loading state
  if (loading && tenants.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Tenants</h1>
            <p className="text-muted-foreground">Manage your tenant records</p>
          </div>
        </div>
        <Card>
          <CardContent className="text-center py-12">
            <Loader2 className="mx-auto h-12 w-12 text-gray-400 mb-4 animate-spin" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading tenants...</h3>
            <p className="text-gray-500">Please wait while we fetch your data.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state
  if (error && tenants.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Tenants</h1>
            <p className="text-muted-foreground">Manage your tenant records</p>
          </div>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={fetchTenants}
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => setError(null)}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Tenants</h1>
          <p className="text-muted-foreground">Manage your tenant records</p>
        </div>
        <Button
          className="gap-2"
          onClick={handleCreate}
          disabled={!hasPermission('tanant', 'create')}
        >
          <Plus className="h-4 w-4" />
          Add Tenant
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search tenants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tenants List */}
      {filteredTenants.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTenants.map((tenant) => (
            <Card key={tenant.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="font-semibold text-lg text-gray-900">
                    {tenant.name}
                  </h3>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleView(tenant)}>
                        <Settings className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(tenant)}>
                        <Edit2 className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleToggleStatus(tenant)}>
                        <Power className="mr-2 h-4 w-4" />
                        {tenant.status === 'active' ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDelete(tenant)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <img
                  src={getImageUrl(tenant.photo)}
                  alt={`${tenant.first_name} ${tenant.last_name}`}
                  className="w-16 h-16 rounded-full object-cover mb-4"
                />
                <p className="text-gray-600 text-sm mb-1">{tenant.first_name} {tenant.last_name}</p>
                <p className="text-gray-600 text-sm mb-1">{tenant.email}</p>
                <p className="text-gray-600 text-sm mb-4">{tenant.phone}</p>
                <Badge
                  variant={tenant.status === 'active' ? 'default' : 'secondary'}
                  className={tenant.status === 'active' ? 'bg-green-500' : 'bg-gray-500'}
                >
                  {tenant.status === 'active' ? 'Active' : 'Inactive'}
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No tenants found
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all'
                ? 'No tenants match your current filters.'
                : 'Get started by creating your first tenant.'}
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button className="gap-2" onClick={handleCreate}>
                <Plus className="h-4 w-4" />
                Add Tenant
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Modal */}
      <TenantModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSave}
        tenant={selectedTenant}
        mode={modalMode}
      />
    </div>
  );
}
