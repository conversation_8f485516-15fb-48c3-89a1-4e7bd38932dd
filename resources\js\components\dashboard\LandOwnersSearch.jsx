import React from 'react';
import { Search, Filter } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/useTranslation';

const LandOwnersSearch = ({ searchTerm, setSearchTerm, handleSearch }) => {
  const { t } = useTranslation();

  return (
     <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                {t('landOwners.searchTitle')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSearch} className="flex gap-4">
                <div className="flex-1 no-focus-outline">
                  <style jsx>{`
                    .no-focus-outline input {
                      transition: all 0.2s ease-in-out;
                      transform: scale(1);
                    }
                    .no-focus-outline input:focus {
                      outline: none !important;
                      border-color: #d1d5db !important;
                      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                      transform: scale(1.02) !important;
                    }
                    .no-focus-outline input:focus-visible {
                      outline: none !important;
                      border-color: #d1d5db !important;
                      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                      transform: scale(1.02) !important;
                    }
                    .no-focus-outline input:hover {
                      border-color: #9ca3af !important;
                      transform: scale(1.01) !important;
                    }
                  `}</style>
                  <Input
                    placeholder={t('landOwners.searchPlaceholder')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button type="submit">
                  <Search className="mr-2 h-4 w-4" />
                  {t('common.buttons.search')}
                </Button>
              </form>
            </CardContent>
          </Card>
    
  )

};
  export default LandOwnersSearch;