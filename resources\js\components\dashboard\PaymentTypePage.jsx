import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import PaymentTypeSearch from './PaymentTypeSearch';
import PaymentTypeTable from './PaymentTypesTable';
import {
  Plus,
  Settings
} from 'lucide-react';

import paymentTypeAPI from '@/services/paymentTypeAPI';

export default function PaymentTypePage() {
  const [paymentTypes, setPaymentTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [editPaymentType, setEditPaymentType] = useState(null);
  const [saving, setSaving] = useState(false);
  const [form, setForm] = useState({
    name: '',
    description: '',
    is_active: true,
    sort_order: 0,
    icon: '',
    color: ''
  });

  // Fetch all payment types
  const fetchPaymentTypes = async () => {
    setLoading(true);
    try { 
      const data = await paymentTypeAPI.getAll();
      if (data.success && data.data && data.data.data) {
        setPaymentTypes(data.data.data);
      } else {
        setPaymentTypes(data.data || data);
      }
    } catch (err) {
      setError(err.message || 'Failed to fetch payment types');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPaymentTypes();
  }, []);

  // CRUD handlers
  const handleCreate = () => {
    setEditPaymentType(null);
    setForm({
      name: '',
      description: '',
      is_active: true,
      sort_order: 0,
      icon: '',
      color: ''
    });
    setModalOpen(true);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      if (editPaymentType) {
        await paymentTypeAPI.update(editPaymentType.id, form);
      } else {
        await paymentTypeAPI.create(form);
      }
      setModalOpen(false);
      setForm({
        name: '',
        description: '',
        is_active: true,
        sort_order: 0,
        icon: '',
        color: ''
      });
      setEditPaymentType(null);
      fetchPaymentTypes();
    } catch (err) {
      setError(err.message || 'Failed to save payment type');
    } finally {
      setSaving(false);
    }
  };

  const handleEdit = (paymentType) => {
    setEditPaymentType(paymentType);
    setForm({
      name: paymentType.name,
      description: paymentType.description || '',
      is_active: paymentType.is_active,
      sort_order: paymentType.sort_order || 0,
      icon: paymentType.icon || '',
      color: paymentType.color || ''
    });
    setModalOpen(true);
  };

  const handleDelete = async (paymentType) => {
    if (window.confirm(`Delete payment type "${paymentType.name}"?`)) {
      setSaving(true);
      try {
        await paymentTypeAPI.delete(paymentType.id);
        fetchPaymentTypes();
      } catch (err) {
        setError(err.message || 'Failed to delete payment type');
      } finally {
        setSaving(false);
      }
    }
  };

  const handleToggleStatus = async (paymentType) => {
    setSaving(true);
    try {
      await paymentTypeAPI.toggleStatus(paymentType.id);
      fetchPaymentTypes();
    } catch (err) {
      setError(err.message || 'Failed to toggle status');
    } finally {
      setSaving(false);
    }
  };

  // Filtered list for search and status
  const filteredPaymentTypes = paymentTypes.filter((pt) => {
    const matchesSearch = pt.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all'
        ? true
        : statusFilter === 'active'
        ? pt.is_active
        : !pt.is_active;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Payment Types</h1>
          <p className="text-muted-foreground">
            Manage payment methods and classifications
          </p>
        </div>
        <Button onClick={handleCreate} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Payment Type
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <span className="text-sm font-medium">Error:</span>
              <span className="text-sm">{error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setError(null)}
                className="ml-auto"
              >
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
        <PaymentTypeSearch
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        setSearchTerm={setSearchTerm}
        setStatusFilter={setStatusFilter}
      />

      {/* Loading State */}
      {loading ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading payment types...</p>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Payment Types Grid */}
          {filteredPaymentTypes.length > 0 ? (
            <PaymentTypeTable
              filteredPaymentTypes={filteredPaymentTypes}
              handleEdit={handleEdit}
              handleToggleStatus={handleToggleStatus}
              handleDelete={handleDelete}
            />
          
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No payment types found
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all'
                ? 'No payment types match your current filters.'
                : 'Get started by creating your first payment type.'}
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button onClick={handleCreate} className="gap-2">
                <Plus className="h-4 w-4" />
                Add Payment Type
              </Button>
            )}
          </CardContent>
        </Card>
      )}
        </>
      )}

      {/* Create/Edit Modal */}
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {editPaymentType ? 'Edit Payment Type' : 'Create New Payment Type'}
            </DialogTitle>
            <DialogDescription>
              {editPaymentType
                ? 'Update the payment type details below.'
                : 'Fill in the details to create a new payment type.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={form.name}
                onChange={(e) => setForm({ ...form, name: e.target.value })}
                className="col-span-3"
                placeholder="Payment type name"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea
                id="description"
                value={form.description}
                onChange={(e) => setForm({ ...form, description: e.target.value })}
                className="col-span-3"
                placeholder="Payment type description"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="sort_order" className="text-right">
                Sort Order
              </Label>
              <Input
                id="sort_order"
                type="number"
                value={form.sort_order}
                onChange={(e) => setForm({ ...form, sort_order: parseInt(e.target.value) || 0 })}
                className="col-span-3"
                placeholder="0"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="icon" className="text-right">
                Icon
              </Label>
              <Input
                id="icon"
                value={form.icon}
                onChange={(e) => setForm({ ...form, icon: e.target.value })}
                className="col-span-3"
                placeholder="Icon name or path"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="color" className="text-right">
                Color
              </Label>
              <Input
                id="color"
                type="color"
                value={form.color}
                onChange={(e) => setForm({ ...form, color: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setModalOpen(false)}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleSave}
              disabled={saving || !form.name.trim()}
            >
              {saving ? 'Saving...' : (editPaymentType ? 'Update' : 'Create')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
