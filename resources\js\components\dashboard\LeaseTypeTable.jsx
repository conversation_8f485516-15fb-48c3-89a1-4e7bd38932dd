
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { Badge, Edit2, Trash2, MoreHorizontal, Power } from 'lucide-react';
const LeaseTypeTable = ({
  leaseType ,
  handleEdit,
  handleToggleStatus,
  handleDelete,
}) => {
  return (
    <Card key={leaseType.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg text-gray-900 mb-2">
                      {leaseType.name}
                    </h3>
                    {leaseType.description && (
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {leaseType.description}
                      </p>
                    )}
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleEdit(leaseType)}>
                        <Edit2 className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleToggleStatus(leaseType)}>
                        <Power className="mr-2 h-4 w-4" />
                        {leaseType.is_active ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDelete(leaseType)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="flex justify-between items-center">
                  <Badge
                    variant={leaseType.is_active ? 'default' : 'secondary'}
                    className={leaseType.is_active ? 'bg-green-500' : 'bg-gray-500'}
                  >
                    {leaseType.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                  <div className="text-sm text-gray-500">
                    Order: {leaseType.sort_order}
                  </div>
                </div>
              </CardContent>
            </Card>
  )};
   export default LeaseTypeTable;
