import axios from 'axios';

const API_URL = '/api/property-types';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

const propertyTypeAPI = {
 
    // Toggle status
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data;
        } catch (error) {
            console.error('Error toggling property type status:', error);
            throw error.response?.data || error;
        }
    },
  
    // // Get property types for dropdown
    // dropdown: async () => {
    //     try {
    //         const response = await api.get('/api/property-types-dropdown');
    //         return response.data;
    //     } catch (error) {
    //         console.error('Error fetching property types dropdown:', error);
    //         throw error.response?.data || error;
    //     }
    // }
};

export default propertyTypeAPI;
