import axios from 'axios';

const API_URL = '/api/property-types';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

const propertyTypeAPI = {
    // Get all property types with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching property types:', error);
            throw error.response?.data || error;
        }
    },

    // Get property type by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching property type:', error);
            throw error.response?.data || error;
        }
    },

    // Create new property type
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data;
        } catch (error) {
            console.error('Error creating property type:', error);
            throw error.response?.data || error;
        }
    },

    // Update property type
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data;
        } catch (error) {
            console.error('Error updating property type:', error);
            throw error.response?.data || error;
        }
    },

    // Delete property type
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting property type:', error);
            throw error.response?.data || error;
        }
    },

    // Toggle status
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data;
        } catch (error) {
            console.error('Error toggling property type status:', error);
            throw error.response?.data || error;
        }
    },
  
    // Get property types for dropdown
    dropdown: async () => {
        try {
            const response = await api.get(`${API_URL}/dropdown`);
            return response.data;
        } catch (error) {
            console.error('Error fetching property types dropdown:', error);
            throw error.response?.data || error;
        }
    }
};

export default propertyTypeAPI;
