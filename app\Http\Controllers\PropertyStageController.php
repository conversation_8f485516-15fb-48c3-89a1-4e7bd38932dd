<?php

namespace App\Http\Controllers;
use App\Models\PropertyStage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
  
class PropertyStageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try {
            $query = PropertyStage::query();

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%");
                });
            }
            // Sorting
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortDirection = $request->get('sort_direction', 'asc');
            if (in_array($sortBy, ['name', 'description', 'sort_order', 'created_at'])) {
                $query->orderBy($sortBy, $sortDirection);
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $propertyStages = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $propertyStages,
                'message' => 'Property stages retrieved successfully'
            ]);
        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving property stages: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:property_stages,name',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $propertyStage = PropertyStage::create($validated);
            return response()->json([
                'success' => true,
                'data' => $propertyStage,
                'message' => 'Property stage created successfully'
            ], 201);
        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating property stage: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        //
        try{
            $propertyStage = PropertyStage::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $propertyStage,
                'message' => 'Property stage retrieved successfully'
            ]);
        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving property stage: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        //
        try{ 
            $propertyStage = PropertyStage::findOrFail($id);
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:property_stages,name,' . $id,
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $propertyStage->update($validated);
            return response()->json([
                'success' => true,
                'data' => $propertyStage->fresh(),
                'message' => 'Property stage updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Error updating property stage: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        //
        try{
            $propertyStage = PropertyStage::findOrFail($id);
            $propertyStage->delete();
            return response()->json([
                'success' => true,
                'message' => 'Property stage deleted successfully'
            ]);
        }catch(\Exception $e){
            return response()->Json([
                'success' => false,
                'message' => 'Error deleting property stage: ' . $e->getMessage()
            ],500);
        }
    }
    public function toggleStatus(PropertyStage $propertyStage): JsonResponse
    {
        try {
            $propertyStage->is_active = !$propertyStage->is_active;
            $propertyStage->save();

            return response()->json([
                'success' => true,
                'data' => $propertyStage,
                'message' => 'Property stage status updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property stage status',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    public function dropdown(): JsonResponse
    {
        try {
            $propertyStages = PropertyStage::active()
                ->ordered()
                ->select('id', 'name', 'description')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $propertyStages,
                'message' => 'Property stages for dropdown retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property stages for dropdown'
            ], 500);
        }
    }

    public function statistics(): JsonResponse
    {
        try {
            $total = PropertyStage::count();
            $active = PropertyStage::where('is_active', true)->count();
            $inactive = PropertyStage::where('is_active', false)->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'total' => $total,
                    'active' => $active,
                    'inactive' => $inactive
                ],
                'message' => 'Property stage statistics retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property stage statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
