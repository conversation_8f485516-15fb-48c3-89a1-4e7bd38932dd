import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import UnitTypeSearch from './UnitTypeSearch';
import UnitTypeTable from './unitTypeTable';
import {
  Plus,
  Settings,
  AlertCircle,
  Loader2
} from 'lucide-react';


import UnitTypeModal from './UnitTypeModal';
import unitTypeAPI from '../../services/unitTypeAPI';
import { useAuth } from '../../contexts/AuthContext';


export default function UnitTypePage() {
  // Handler for delete
  const handleDelete = async (unitType) => {
    if (!hasPermission('unit-type', 'delete')) {
      setError('You do not have permission to delete unit types.');
      return;
    }
    if (window.confirm('Are you sure you want to delete this unit type?')) {
      setLoading(true);
      try {
        await unitTypeAPI.delete(unitType.id);
        fetchUnitTypes();
      } catch (err) {
        setError(err.message || 'Failed to delete unit type');
      } finally {
        setLoading(false);
      }
    }
  };

  // Handler for toggle status
  const handleToggleStatus = async (unitType) => {
    if (!hasPermission('unit-type', 'update')) {
      setError('You do not have permission to update unit types.');
      return;
    }
    setLoading(true);
    try {
      await unitTypeAPI.toggleStatus(unitType.id);
      fetchUnitTypes();
    } catch (err) {
      setError(err.message || 'Failed to update unit type status');
    } finally {
      setLoading(false);
    }
  };
  const { hasPermission, isAuthenticated } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedUnitType, setSelectedUnitType] = useState(null);
  const [unitTypes, setUnitTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchUnitTypes = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user has permission to read unit types
      if (!hasPermission('unit-type', 'read')) {
        setError('You do not have permission to view unit types.');
        return;
      }

      const response = await unitTypeAPI.getAll();

      // Handle the response structure properly
      if (response.success) {
        const data = response.data?.data || response.data || [];
        setUnitTypes(Array.isArray(data) ? data : []);
      } else {
        setError(response.message || 'Failed to fetch unit types');
      }
    } catch (error) {
      console.error('Error fetching unit types:', error);
      if (error.status === 401) {
        setError('Authentication required. Please log in again.');
      } else if (error.status === 403) {
        setError('You do not have permission to view unit types.');
      } else {
        setError(error.message || 'Failed to fetch unit types. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchUnitTypes();
    }
  }, [isAuthenticated]);

  // Handler for modal success - refresh data
  const handleModalSuccess = () => {
    setIsModalOpen(false);
    fetchUnitTypes(); // Refresh the data after successful operation
  };

  // Handlers for create, edit, view with permission checks
  const handleCreate = () => {
    if (!hasPermission('unit-type', 'create')) {
      setError('You do not have permission to create unit types.');
      return;
    }
    setSelectedUnitType(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleEdit = (unitType) => {
    if (!hasPermission('unit-type', 'update')) {
      setError('You do not have permission to edit unit types.');
      return;
    }
    setSelectedUnitType(unitType);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleView = (unitType) => {
    setSelectedUnitType(unitType);
    setModalMode('view');
    setIsModalOpen(true);
  };

  // Apply filters
  const filteredUnitTypes = Array.isArray(unitTypes)
    ? unitTypes.filter((unit) => {
        const matchesSearch = unit.name?.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus =
          statusFilter === 'all' ||
          (statusFilter === 'active' && unit.is_active) ||
          (statusFilter === 'inactive' && !unit.is_active);
        return matchesSearch && matchesStatus;
      })
    : [];

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Unit Types</h1>
            <p className="text-muted-foreground">
              Manage your available unit type categories
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="text-center py-12">
            <Loader2 className="mx-auto h-12 w-12 text-gray-400 mb-4 animate-spin" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading unit types...</h3>
            <p className="text-gray-500">Please wait while we fetch your data.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Unit Types</h1>
            <p className="text-muted-foreground">
              Manage your available unit type categories
            </p>
          </div>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={fetchUnitTypes}
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Unit Types</h1>
          <p className="text-muted-foreground">
            Manage your available unit type categories
          </p>
        </div>
        <Button
          className="gap-2"
          onClick={handleCreate}
          disabled={!hasPermission('unit-type', 'create')}
        >
          <Plus className="h-4 w-4" />
          Add Unit Type
        </Button>
      </div>

      {/* Filters */}
      <UnitTypeSearch
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        setSearchTerm={setSearchTerm}
        setStatusFilter={setStatusFilter}
      />

      {/* Unit Types Grid */}
      {filteredUnitTypes.length > 0 ? (
       <UnitTypeTable
          filteredUnitTypes={filteredUnitTypes}
          handleView={handleView}
          handleEdit={handleEdit}
          handleToggleStatus={handleToggleStatus}
          handleDelete={handleDelete}
          hasPermission={hasPermission}
        />
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No unit types found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all'
                ? 'No unit types match your current filters.'
                : 'Get started by creating your first unit type.'}
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button className="gap-2" onClick={handleCreate}>
                <Plus className="h-4 w-4" />
                Add Unit Type
              </Button>
            )}
          </CardContent>
        </Card>
      )}

       {/* Modal */}
      <UnitTypeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleModalSuccess}
        unitType={selectedUnitType}
        mode={modalMode}
      />
    </div>
  );
}
