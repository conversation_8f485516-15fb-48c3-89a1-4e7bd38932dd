import axios from "axios";

const API_URL = '/api/payment-statuses';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add JWT token to request headers if available
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Payment Status API functions
const paymentStatusAPI = {
    // Get all payment statuses with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            // Handle paginated response
            if (response.data.success && response.data.data && response.data.data.data) {
                return response.data; // Return full response for pagination info
            }
            return response.data;
        } catch (error) {
            console.error('Error fetching payment statuses:', error);
            throw error.response?.data || error;
        }
    },

    // Get payment status by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching payment status:', error);
            throw error.response?.data || error;
        }
    },

    // Create new payment status
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error creating payment status:', error);
            throw error.response?.data || error;
        }
    },

    // Update payment status
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error updating payment status:', error);
            throw error.response?.data || error;
        }
    },

    // Delete payment status
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error deleting payment status:', error);
            throw error.response?.data || error;
        }
    },

    // Toggle payment status status (active/inactive)
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error toggling payment status status:', error);
            throw error.response?.data || error;
        }
    },

    // Get dropdown options (active payment statuses only)
    getDropdown: async () => {
        try {
            const response = await api.get(`${API_URL}-dropdown`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching payment statuses dropdown:', error);
            throw error.response?.data || error;
        }
    }
}

export default paymentStatusAPI;
