
import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  X,
  Settings
} from 'lucide-react'
import { Label } from '@/components/ui/label';

const RentTypeModal = ({
  isOpen,
  onClose,
  onSuccess,
  rentType,
  mode
}) => {
  const [form, setForm] = useState({
    name: '',
    description: '',
    sort_order: 0,
    is_active: true
  });
  const [saving, setSaving] = useState(false);

  // Initialize form when modal opens or rentType changes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && rentType) {
        setForm({
          name: rentType.name || '',
          description: rentType.description || '',
          sort_order: rentType.sort_order || 0,
          is_active: rentType.is_active ?? true
        });
      } else {
        setForm({
          name: '',
          description: '',
          sort_order: 0,
          is_active: true
        });
      }
    }
  }, [isOpen, mode, rentType]);

  const handleSave = async () => {
    if (!form.name.trim()) return;

    setSaving(true);
    try {
      // Here you would call your API to save the rent type
      // For now, just simulate a save
      await new Promise(resolve => setTimeout(resolve, 1000));

      onSuccess(form);
      onClose();
    } catch (error) {
      console.error('Error saving rent type:', error);
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-2xl w-full max-w-md max-h-[90vh] flex flex-col overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
              <div className="flex items-center space-x-3">
                <Settings className="h-6 w-6" />
                <div>
                  <h3 className="text-lg font-semibold">
                    {mode === 'edit' ? 'Edit Rent Type' : 'Create New Rent Type'}
                  </h3>
                  <p className="text-blue-100 text-sm">
                    {mode === 'edit'
                      ? 'Update the rent type details below.'
                      : 'Fill in the details to create a new rent type.'}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            {/* Modal Content */}
            <div className="flex-1 flex flex-col overflow-y-auto px-6 py-4 min-h-0">
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={form.name}
                    onChange={(e) => setForm({ ...form, name: e.target.value })}
                    className="col-span-3"
                    placeholder="Rent type name"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    value={form.description}
                    onChange={(e) => setForm({ ...form, description: e.target.value })}
                    className="col-span-3"
                    placeholder="Rent type description"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="sort_order" className="text-right">
                    Sort Order
                  </Label>
                  <Input
                    id="sort_order"
                    type="number"
                    value={form.sort_order}
                    onChange={(e) => setForm({ ...form, sort_order: parseInt(e.target.value) || 0 })}
                    className="col-span-3"
                    placeholder="0"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="is_active" className="text-right">
                    Active
                  </Label>
                  <div className="col-span-3 flex items-center space-x-2">
                    <Checkbox
                      id="is_active"
                      checked={form.is_active}
                      onCheckedChange={(checked) => setForm({ ...form, is_active: checked })}
                    />
                    <Label htmlFor="is_active" className="text-sm font-normal">
                      Rent type is active
                    </Label>
                  </div>
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={saving}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleSave}
                  disabled={saving || !form.name.trim()}
                >
                  {saving ? 'Saving...' : (mode === 'edit' ? 'Update' : 'Create')}
                </Button>
              </div>
            </div>
          </div>
        </div>
  )};
  export default RentTypeModal;
