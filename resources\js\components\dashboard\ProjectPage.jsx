import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { Loader2, Plus, Search, Edit2, Trash2, Eye, Building2, MapPin, DollarSign, Camera, Video, Home } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import ProjectModal from './ProjectModal';
import projectAPI from '@/services/projectAPI';

const ProjectPage = () => {
  const { t } = useTranslation();
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all'); 
  const [typeFilter, setTypeFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [statistics, setStatistics] = useState({
    total: 0,
    featured: 0,
    available: 0,
    completed: 0,
    planning: 0,
    under_construction: 0,
    sold: 0,
    rented: 0
  });
  const [showModal, setShowModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'
  // Helper to strip HTML tags
  const stripHtml = (html) => {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  };
  // Property types and statuses
  const propertyTypes = {
    residential: 'Residential',
    commercial: 'Commercial',
    industrial: 'Industrial',
    land: 'Land',
    mixed: 'Mixed Use'
  };

  const propertyStatuses = {
    planning: 'Planning',
    under_construction: 'Under Construction',
    completed: 'Completed',
    sold: 'Sold',
    rented: 'Rented',
    maintenance: 'Under Maintenance'
  };

  // Fetch projects
  const fetchProjects = async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        per_page: perPage,
        search: searchTerm,
        status: statusFilter !== 'all' ? statusFilter : '',
        property_type: typeFilter !== 'all' ? typeFilter : '',
        sort_by: sortBy,
        sort_order: sortOrder
      };

      const response = await projectAPI.getAll(params);
      setProjects(response.data.data);
      setCurrentPage(response.data.current_page);
      setTotalPages(response.data.last_page);
      // Only update statistics if they exist in the response
      if (response.data.statistics) {
        setStatistics(response.data.statistics);
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast({
        title: "Error",
        description: "Failed to fetch projects",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await projectAPI.getStatistics();
      if (response.success && response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
      // Keep the default values from initial state
    }
  };

  useEffect(() => {
    fetchProjects(1);
    fetchStatistics();
  }, [perPage, searchTerm, statusFilter, typeFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    fetchProjects(1);
  };

  // Handle modal operations
  const handleCreate = () => {
    setSelectedProject(null);
    setModalMode('create');
    setShowModal(true);
  };

  const handleEdit = (project) => {
    setSelectedProject(project);
    setModalMode('edit');
    setShowModal(true);
  };

  const handleView = (project) => {
    setSelectedProject(project);
    setModalMode('view');
    setShowModal(true);
  };

  const handleDelete = async (project) => {
    if (window.confirm('Are you sure you want to delete this project?')) {
      try {
        await projectAPI.delete(project.id);
        toast({
          title: "Success",
          description: "Project deleted successfully",
        });
        fetchProjects(currentPage);
        fetchStatistics();
      } catch (error) {
        toast({
          title: "Error",
          description: error.response?.data?.message || "Failed to delete project",
          variant: "destructive",
        });
      }
    }
  };

  const handleModalSuccess = () => {
    setShowModal(false);
    fetchProjects(currentPage);
    fetchStatistics();
  };

  const getStatusBadgeVariant = (status) => {
    const variants = {
      planning: 'secondary',
      under_construction: 'outline',
      completed: 'default',
      sold: 'destructive',
      rented: 'secondary',
      maintenance: 'outline'
    };
    return variants[status] || 'default';
  };

  return (
    <div className=" mx-auto p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <Building2 className="h-8 w-8" />
            Properties
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Manage your property portfolio
          </p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="h-4 w-4 mr-2" />
          Add Property
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Properties</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.total || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Featured</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.featured || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.available || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.completed || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search properties..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Property Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {Object.entries(propertyTypes).map(([key, value]) => (
                    <SelectItem key={key} value={key}>{value}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {Object.entries(propertyStatuses).map(([key, value]) => (
                    <SelectItem key={key} value={key}>{value}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button type="submit" disabled={loading}>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Projects Table */}
      <Card>
        <CardHeader>
          <CardTitle>Properties</CardTitle>
          <CardDescription>
            Manage your property listings and details
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="space-y-4">
              {projects.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No properties found</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {projects.map((project) => (
                    <Card key={project.id} className="hover:shadow-md transition-shadow h-full flex flex-col">
                      <CardContent className="pt-6 flex-1 flex flex-col">
                        {/* Project Image */}
                        <div className="flex-shrink-0 mb-4">
                          {project.featured_image ? (
                            <img
                              src={project.featured_image.image_url}
                              alt={project.title}
                              className="w-full h-32 object-cover rounded-lg"
                            />
                          ) : (
                            <div className="w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                              <Building2 className="h-8 w-8 text-gray-400" />
                            </div>
                          )}
                        </div>
                        {/* Project Details */}
                        <div className="flex-1 flex flex-col justify-between space-y-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="text-lg font-semibold">{project.title}</h3>
                              <p className="text-sm text-gray-600 flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                {project.location}
                              </p>
                            </div>
                            <div className="flex gap-2">
                              <Badge variant={getStatusBadgeVariant(project.status)}>
                                {propertyStatuses[project.status]}
                              </Badge>
                              {project.is_featured && (
                                <Badge variant="secondary">Featured</Badge>
                              )}
                            </div>
                          </div>
                          <p className="text-sm text-gray-700 line-clamp-2">
                            {stripHtml(project.description)}
                          </p>
                          <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <Building2 className="h-4 w-4" />
                              {propertyTypes[project.property_type]}
                            </span>
                            {project.area_sqft && (
                              <span>{project.area_sqft} sq ft</span>
                            )}
                            {project.bedrooms && (
                              <span>{project.bedrooms} beds</span>
                            )}
                            {project.bathrooms && (
                              <span>{project.bathrooms} baths</span>
                            )}
                            {project.units_count && (
                              <span>{project.units_count} units</span>
                            )}
                          </div>
                          <div className="flex justify-between items-center pt-2">
                            <div className="flex gap-2">
                              {project.images_count > 0 && (
                                <Badge variant="outline" className="text-xs">
                                  <Camera className="h-3 w-3 mr-1" />
                                  {project.images_count}
                                </Badge>
                              )}
                              {project.videos_count > 0 && (
                                <Badge variant="outline" className="text-xs">
                                  <Video className="h-3 w-3 mr-1" />
                                  {project.videos_count}
                                </Badge>
                              )}
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleView(project)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEdit(project)}
                              >
                                <Edit2 className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDelete(project)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-6">
                  <Button
                    variant="outline"
                    onClick={() => fetchProjects(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => fetchProjects(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Project Modal */}
      <ProjectModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        project={selectedProject}
        mode={modalMode}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
};

export default ProjectPage;
