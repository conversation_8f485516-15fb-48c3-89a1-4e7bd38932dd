  import React, { useState, useEffect } from 'react';
  import { Button } from '@/components/ui/button';
  import { Input } from '@/components/ui/input';
  import { Badge } from '@/components/ui/badge';
  import { Card, CardContent } from '@/components/ui/card';
  import PaymentStatusSearch from './paymentStatusSearch';
  import PaymentStatusTable from './PaymentStatusTable';
  import PaymentStatusModal from './PaymentStatusModal';
  import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
  } from '@/components/ui/dialog';
  import { Label } from '@/components/ui/label';
  import { Textarea } from '@/components/ui/textarea';
  import {
    Plus,
    Settings
  } from 'lucide-react';
  
  import paymentStatusAPI from '@/services/paymentStatusAPI';
  export default function PaymentStatusPage() {
    const [paymentStatuses, setPaymentStatuses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [modalOpen, setModalOpen] = useState(false);
    const [editPaymentStatus, setEditPaymentStatus] = useState(null);
    const [saving, setSaving] = useState(false);
    const [form, setForm] = useState({
      name: '',
      description: '',
      is_active: true,
      sort_order: 0,
    });

    // Fetch all payment statuses
    const fetchPaymentStatuses = async () => {
      setLoading(true);
      try {
        const data = await paymentStatusAPI.getAll();
        if (data.success && data.data && data.data.data) {
          setPaymentStatuses(data.data.data);
        } else {
          setPaymentStatuses(data.data || data);
        }
      } catch (err) {
        setError(err.message || 'Failed to fetch payment statuses');
      } finally {
        setLoading(false);
      }
    };

    useEffect(() => {
      fetchPaymentStatuses();
    }, []);

    // CRUD handlers
    const handleCreate = () => {
      setEditPaymentStatus(null);
      setForm({
        name: '',
        description: '',
        is_active: true,
        sort_order: 0,
      });
      setModalOpen(true);
    };

    const handleEdit = (paymentStatus) => {
      setEditPaymentStatus(paymentStatus);
      setForm({
        name: paymentStatus.name,
        description: paymentStatus.description || '',
        is_active: paymentStatus.is_active,
        sort_order: paymentStatus.sort_order || 0,
      });
      setModalOpen(true);
    };

    const handleSave = async () => {
      setSaving(true);
      try {
        if (editPaymentStatus) {
          await paymentStatusAPI.update(editPaymentStatus.id, form);
        } else {
          await paymentStatusAPI.create(form);
        }
        setModalOpen(false);
        setForm({
          name: '',
          description: '',
          is_active: true,
          sort_order: 0,
        });
        setEditPaymentStatus(null);
        fetchPaymentStatuses();
      } catch (err) {
        setError(err.message || 'Failed to save payment status');
      } finally {
        setSaving(false);
      }
    };

    const handleToggleStatus = async (paymentStatus) => {
      setSaving(true);
      try {
        await paymentStatusAPI.toggleStatus(paymentStatus.id);
        fetchPaymentStatuses();
      } catch (err) {
        setError(err.message || 'Failed to toggle status');
      } finally {
        setSaving(false);
      }
    };

    const handleDelete = async (paymentStatus) => {
      if (window.confirm(`Delete payment status "${paymentStatus.name}"?`)) {
        setSaving(true);
        try {
          await paymentStatusAPI.delete(paymentStatus.id);
          fetchPaymentStatuses();
        } catch (err) {
          setError(err.message || 'Failed to delete payment status');
        } finally {
          setSaving(false);
        }
      }
    };

    // Filtered list for search and status
    const filteredStatuses = paymentStatuses.filter((ps) => {
      const matchesSearch = ps.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus =
        statusFilter === 'all'
          ? true
          : statusFilter === 'active'
          ? ps.is_active
          : !ps.is_active;
      return matchesSearch && matchesStatus;
    });

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Payment Statuses</h1>
            <p className="text-muted-foreground">
              Manage payment status classifications
            </p>
          </div>
          <Button onClick={handleCreate} className="gap-2">
            <Plus className="h-4 w-4" />
            Add Payment Status
          </Button>
        </div>
        {/* error message */}
        {error && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-2 text-red-600">
                    <span className="text-sm font-medium">Error:</span>
                    <span className="text-sm">{error}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setError(null)}
                      className="ml-auto"
                    >
                      Dismiss
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

        {/* Filters */}
            <PaymentStatusSearch
              searchTerm={searchTerm}
              statusFilter={statusFilter}
              setSearchTerm={setSearchTerm}
              setStatusFilter={setStatusFilter}
            />

        {/* Loading State */}
          {loading ? (
          <Card>
            <CardContent className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading payment statuses...</p>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Payment Status Grid */}
        {filteredStatuses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredStatuses.map((status) => (
                <PaymentStatusTable
                  key={status.id}
                  status={status}
                  handleEdit={handleEdit}
                  handleToggleStatus={handleToggleStatus}
                  handleDelete={handleDelete}
                />
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No payment statuses found
              </h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || statusFilter !== 'all'
                  ? 'No payment statuses match your current filters.'
                  : 'Get started by creating your first payment status.'}
              </p>
              {(!searchTerm && statusFilter === 'all') && (
                <Button onClick={handleCreate} className="gap-2">
                  <Plus className="h-4 w-4" />
                  Add Payment Status
                </Button>
              )}
            </CardContent>
          </Card>
        )}
          </>
        )}

        {/* Create/Edit Modal */}
        <PaymentStatusModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          form={form}
          setForm={setForm}
          onSave={handleSave}
          mode={editPaymentStatus ? 'edit' : 'create'}
          saving={saving}
          modalOpen = {modalOpen}
          setModalOpen = {setModalOpen}
          editPaymentStatus = {editPaymentStatus}
          handleSave = {handleSave}
        />
      </div>
    );
  }
