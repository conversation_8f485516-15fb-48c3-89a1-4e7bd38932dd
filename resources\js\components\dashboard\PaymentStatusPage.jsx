  import React, { useState, useEffect } from 'react';
  import { But<PERSON> } from '@/components/ui/button';
  import { Input } from '@/components/ui/input';
  import { Badge } from '@/components/ui/badge';
  import { Card, CardContent } from '@/components/ui/card';
  import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
  } from '@/components/ui/dialog';
  import { Label } from '@/components/ui/label';
  import { Textarea } from '@/components/ui/textarea';
  import {
    Search,
    Plus,
    Edit2,
    Trash2,
    Power,
    MoreHorizontal,
    Settings
  } from 'lucide-react';
  import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
  } from '@/components/ui/dropdown-menu';
  import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
  import paymentStatusAPI from '@/services/paymentStatusAPI';
  export default function PaymentStatusPage() {
    const [paymentStatuses, setPaymentStatuses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [modalOpen, setModalOpen] = useState(false);
    const [editPaymentStatus, setEditPaymentStatus] = useState(null);
    const [saving, setSaving] = useState(false);
    const [form, setForm] = useState({
      name: '',
      description: '',
      is_active: true,
      sort_order: 0,
    });

    // Fetch all payment statuses
    const fetchPaymentStatuses = async () => {
      setLoading(true);
      try {
        const data = await paymentStatusAPI.getAll();
        if (data.success && data.data && data.data.data) {
          setPaymentStatuses(data.data.data);
        } else {
          setPaymentStatuses(data.data || data);
        }
      } catch (err) {
        setError(err.message || 'Failed to fetch payment statuses');
      } finally {
        setLoading(false);
      }
    };

    useEffect(() => {
      fetchPaymentStatuses();
    }, []);

    // CRUD handlers
    const handleCreate = () => {
      setEditPaymentStatus(null);
      setForm({
        name: '',
        description: '',
        is_active: true,
        sort_order: 0,
      });
      setModalOpen(true);
    };

    const handleEdit = (paymentStatus) => {
      setEditPaymentStatus(paymentStatus);
      setForm({
        name: paymentStatus.name,
        description: paymentStatus.description || '',
        is_active: paymentStatus.is_active,
        sort_order: paymentStatus.sort_order || 0,
      });
      setModalOpen(true);
    };

    const handleSave = async () => {
      setSaving(true);
      try {
        if (editPaymentStatus) {
          await paymentStatusAPI.update(editPaymentStatus.id, form);
        } else {
          await paymentStatusAPI.create(form);
        }
        setModalOpen(false);
        setForm({
          name: '',
          description: '',
          is_active: true,
          sort_order: 0,
        });
        setEditPaymentStatus(null);
        fetchPaymentStatuses();
      } catch (err) {
        setError(err.message || 'Failed to save payment status');
      } finally {
        setSaving(false);
      }
    };

    const handleToggleStatus = async (paymentStatus) => {
      setSaving(true);
      try {
        await paymentStatusAPI.toggleStatus(paymentStatus.id);
        fetchPaymentStatuses();
      } catch (err) {
        setError(err.message || 'Failed to toggle status');
      } finally {
        setSaving(false);
      }
    };

    const handleDelete = async (paymentStatus) => {
      if (window.confirm(`Delete payment status "${paymentStatus.name}"?`)) {
        setSaving(true);
        try {
          await paymentStatusAPI.delete(paymentStatus.id);
          fetchPaymentStatuses();
        } catch (err) {
          setError(err.message || 'Failed to delete payment status');
        } finally {
          setSaving(false);
        }
      }
    };

    // Filtered list for search and status
    const filteredStatuses = paymentStatuses.filter((ps) => {
      const matchesSearch = ps.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus =
        statusFilter === 'all'
          ? true
          : statusFilter === 'active'
          ? ps.is_active
          : !ps.is_active;
      return matchesSearch && matchesStatus;
    });

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Payment Statuses</h1>
            <p className="text-muted-foreground">
              Manage payment status classifications
            </p>
          </div>
          <Button onClick={handleCreate} className="gap-2">
            <Plus className="h-4 w-4" />
            Add Payment Status
          </Button>
        </div>
        {/* error message */}
        {error && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-2 text-red-600">
                    <span className="text-sm font-medium">Error:</span>
                    <span className="text-sm">{error}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setError(null)}
                      className="ml-auto"
                    >
                      Dismiss
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search payment statuses..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
          {loading ? (
          <Card>
            <CardContent className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading payment statuses...</p>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Payment Status Grid */}
        {filteredStatuses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredStatuses.map((status) => (
              <Card key={status.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg text-gray-900 mb-2">
                        {status.name}
                      </h3>
                      {status.description && (
                        <p className="text-gray-600 text-sm line-clamp-2">
                          {status.description}
                        </p>
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleEdit(status)}>
                          <Edit2 className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleToggleStatus(status)}>
                          <Power className="mr-2 h-4 w-4" />
                          {status.is_active ? 'Deactivate' : 'Activate'}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(status)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="flex justify-between items-center">
                    <Badge
                      variant={status.is_active ? 'default' : 'secondary'}
                      className={status.is_active ? 'bg-green-500' : 'bg-gray-500'}
                    >
                      {status.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                    <div className="text-sm text-gray-500">
                      Order: {status.sort_order}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No payment statuses found
              </h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || statusFilter !== 'all'
                  ? 'No payment statuses match your current filters.'
                  : 'Get started by creating your first payment status.'}
              </p>
              {(!searchTerm && statusFilter === 'all') && (
                <Button onClick={handleCreate} className="gap-2">
                  <Plus className="h-4 w-4" />
                  Add Payment Status
                </Button>
              )}
            </CardContent>
          </Card>
        )}
          </>
        )}

        {/* Create/Edit Modal */}
        <Dialog open={modalOpen} onOpenChange={setModalOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {editPaymentStatus ? 'Edit Payment Status' : 'Create New Payment Status'}
              </DialogTitle>
              <DialogDescription>
                {editPaymentStatus
                  ? 'Update the payment status details below.'
                  : 'Fill in the details to create a new payment status.'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={form.name}
                  onChange={(e) => setForm({ ...form, name: e.target.value })}
                  className="col-span-3"
                  placeholder="Payment status name"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={form.description}
                  onChange={(e) => setForm({ ...form, description: e.target.value })}
                  className="col-span-3"
                  placeholder="Payment status description"
                  rows={3}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="sort_order" className="text-right">
                  Sort Order
                </Label>
                <Input
                  id="sort_order"
                  type="number"
                  value={form.sort_order}
                  onChange={(e) => setForm({ ...form, sort_order: parseInt(e.target.value) || 0 })}
                  className="col-span-3"
                  placeholder="0"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setModalOpen(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleSave}
                disabled={saving || !form.name.trim()}
              >
                {saving ? 'Saving...' : (editPaymentStatus ? 'Update' : 'Create')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  }
