import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

const PaymentTypeModal = ({ isOpen, onClose, form, setForm, onSave, mode, saving }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {mode === 'edit' ? 'Edit Payment Type' : mode === 'view' ? 'View Payment Type' : 'Create Payment Type'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'edit' && 'Update the payment type details below.'}
            {mode === 'view' && 'View payment type details.'}
            {mode === 'create' && 'Fill in the details to create a new payment type.'}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">Name</Label>
            <Input
              id="name"
              value={form.name}
              onChange={e => setForm({ ...form, name: e.target.value })}
              className="col-span-3"
              placeholder="Payment type name"
              disabled={mode === 'view'}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">Description</Label>
            <Textarea
              id="description"
              value={form.description}
              onChange={e => setForm({ ...form, description: e.target.value })}
              className="col-span-3"
              placeholder="Payment type description"
              rows={3}
              disabled={mode === 'view'}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sort_order" className="text-right">Sort Order</Label>
            <Input
              id="sort_order"
              type="number"
              value={form.sort_order}
              onChange={e => setForm({ ...form, sort_order: parseInt(e.target.value) || 0 })}
              className="col-span-3"
              placeholder="0"
              disabled={mode === 'view'}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="icon" className="text-right">Icon</Label>
            <Input
              id="icon"
              value={form.icon}
              onChange={e => setForm({ ...form, icon: e.target.value })}
              className="col-span-3"
              placeholder="Icon name or path"
              disabled={mode === 'view'}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="color" className="text-right">Color</Label>
            <Input
              id="color"
              type="color"
              value={form.color}
              onChange={e => setForm({ ...form, color: e.target.value })}
              className="col-span-3"
              disabled={mode === 'view'}
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Close
          </Button>
          {mode !== 'view' && (
            <Button type="button" onClick={onSave} disabled={saving || !form.name.trim()}>
              {saving ? 'Saving...' : (mode === 'edit' ? 'Update' : 'Create')}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentTypeModal;
