<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PropertyService;
use Illuminate\Http\JsonResponse;
class PropertyServicesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try{
            $query = PropertyService::query();

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%");
                });
            }
            // Sorting
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortDirection = $request->get('sort_direction', 'asc');
            if (in_array($sortBy, ['name', 'description', 'sort_order', 'created_at'])) {
                $query->orderBy($sortBy, $sortDirection);
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $propertyServices = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $propertyServices
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch property services: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try{
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:property_services,name',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $propertyService = PropertyService::create($validated);
            return response()->json([
                'success' => true,
                'data' => $propertyService,
                'message' => 'Property service created successfully'
            ], 201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to create property service: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        //
        try{
            $propertyService = PropertyService::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $propertyService,
                'message' => 'Property service retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property service: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        //
        try{
            $propertyService = PropertyService::findOrFail($id);
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:property_services,name,' . $id,
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $propertyService->update($validated);
            return response()->json([
                'success' => true,
                'data' => $propertyService->fresh(),
                'message' => 'Property service updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property service: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        //
        try{
            $propertyService = PropertyService::findOrFail($id);
            $propertyService->delete();
            return response()->json([
                'success' => true,
                'message' => 'Property service deleted successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete property service: ' . $e->getMessage()
            ], 500);
        }
    }

    public function toggleStatus(string $id): JsonResponse
    {
        try{    
            $propertyService = PropertyService::findOrFail($id);
            $propertyService->is_active = !$propertyService->is_active;
            $propertyService->save();
            return response()->json([
                'success' => true,
                'data' => $propertyService,
                'message' => 'Property service status updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property service status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function dropDown(): JsonResponse
    {
        try{    
            $propertyServices = PropertyService::active()
                ->orderBy('sort_order','Asc')
                ->select('id', 'name', 'description', 'icon')
                ->get();
            return response()->json([
                'success' => true,
                'data' => $propertyServices,
                'message' => 'Property services for dropdown retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property services for dropdown',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
