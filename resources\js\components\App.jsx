import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './theme/ThemeProvider';
import { AuthProvider } from '../contexts/AuthContext';
import TranslationProvider from './TranslationProvider';
import AuthGate from './auth/AuthGate';
import DashboardLayout from './layout/DashboardLayout';
import DashboardOverview from './dashboard/DashboardOverview';
import PropertyStatus from './dashboard/PropertyStatusManagement';
import SettingsPage from './dashboard/SettingsPage';
import CustomerPage from './dashboard/customerPage';
import LandOwnersPage from './dashboard/LandOwnersPage';
import LandAcquisitionPage from './dashboard/LandAcquisitionPage';
import PropertyTypes from '../pages/PropertyTypes/PropertyTypes';
import RolePage from './dashboard/RolePage';
import CountryPage from './dashboard/CountryPage';
import StatePage from './dashboard/StatePage';
import AuthTestPage from './dashboard/AuthTestPage';
import PlaceholderPage from './dashboard/PlaceholderPage';
import PermissionRoute from './auth/PermissionRoute';
import LanguagePage from './dashboard/LanguagePage';
import CurrencyPage from './dashboard/CurrencyPage';
import EmployeePage from './dashboard/EmployeePage';
import ContractorPage from './dashboard/ContractorPage';
import VendorTypePage from './dashboard/VendorTypePage';
import VendorPage from './dashboard/VendorPage';
// Assignment pages temporarily commented out - depend on projects
import AssignContractorPage from './dashboard/AssignContractorPage';
import AssignVendorPage from './dashboard/AssignVendorPage';
import AssignEmployeePage from './dashboard/AssignEmployeePage';
import PropertyAmenitiesPage from './dashboard/PropertyAmenitiesPage';
import ModulesSidebarTestPage from '../pages/ModulesSidebarTestPage';
import UnitTypePage from './dashboard/UnitTypePage';
import PropertyStagePage from './dashboard/PropertyStagePage';
import TenantsPage from './dashboard/TenantsPage';
import ProjectPage from './dashboard/ProjectPage';
import PropertyServicePage from './dashboard/PropertyServicePage';
import RentTypePage from './dashboard/RentTypePage';
import LeaseTypePage from './dashboard/LeaseTypePage';
import InvoicePage from './dashboard/InvoicePage';
import PaymentTypePage from './dashboard/PaymentTypePage';
import PaymentStatusPage from './dashboard/PaymentStatusPage';
import PaymentMethodPage from './dashboard/PaymentMethodPage';
import ManageTypesPage from './dashboard/ManageTypesPage';
// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) { 
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('React Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', color: 'red', fontFamily: 'Arial' }}>
          <h1>Something went wrong!</h1>
          <p>Error: {this.state.error?.message}</p>
          <p>Check browser console for details.</p>
        </div>
      );
    }

    return this.props.children;
  }
}

export default function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <TranslationProvider>
          <ThemeProvider defaultTheme="system" storageKey="dashboard-theme">
            <AuthGate>
              <Router>
                <DashboardLayout>
                <Routes>
                  {/* Default route redirects to dashboard */}
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  
                  {/* Dashboard routes */}
                  <Route path="/dashboard" element={<DashboardOverview />} />
                  <Route path="/modules-test" element={<ModulesSidebarTestPage />} />
                <Route 
                  path="/customer" 
                  element={
                    <PermissionRoute requiredModule="customer">
                      <CustomerPage />
                    </PermissionRoute>
                  } 
                />
                
                <Route 
                  path="/land-owners" 
                  element={
                    <PermissionRoute requiredModule="landowners">
                      <LandOwnersPage />
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/all-types" 
                  element={
                    <PermissionRoute requiredModule="all-types">
                      <ManageTypesPage />
                    </PermissionRoute>
                  } 
                />     
                
                
            
                <Route 
                  path="/land-acquisition" 
                  element={
                    <PermissionRoute requiredModule="land-acquisition">
                      <LandAcquisitionPage />
                    </PermissionRoute>
                  } 
                /> 
                
                <Route 
                  path="/projects" 
                  element={
                    <PermissionRoute requiredModule="project">
                      <ProjectPage />
                    </PermissionRoute>
                  } 
                />

                  <Route 
                  path="/property-type" 
                  element={
                    <PermissionRoute requiredModule="property-type">
                      <PropertyTypes/>
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/property-status" 
                  element={
                    <PermissionRoute requiredModule="property-status">
                      <PropertyStatus/>
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/unit-type" 
                  element={
                    <PermissionRoute requiredModule="unit-type">
                      <UnitTypePage/>
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/property-stage" 
                  element={
                    <PermissionRoute requiredModule="property-stage">
                      <PropertyStagePage/>
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/property-service" 
                  element={
                    <PermissionRoute requiredModule="property-service">
                      <PropertyServicePage/>
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/rent-type" 
                  element={
                    <PermissionRoute requiredModule="rent-type">
                      <RentTypePage/>
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/lease-type" 
                  element={
                    <PermissionRoute requiredModule="lease-type">
                      <LeaseTypePage/>
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/invoice" 
                  element={
                    <PermissionRoute requiredModule="invoice">
                      <InvoicePage/>
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/payment-type" 
                  element={
                    <PermissionRoute requiredModule="payment-type">
                      <PaymentTypePage/>
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/payment-status" 
                  element={
                    <PermissionRoute requiredModule="payment-status">
                      <PaymentStatusPage/>
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/payment-method" 
                  element={
                    <PermissionRoute requiredModule="payment-method">
                      <PaymentMethodPage/>
                    </PermissionRoute>
                  } 
                />
                
                <Route 
                  path="/role" 
                  element={
                    <PermissionRoute requiredModule="role">
                      <RolePage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/country" 
                  element={
                    <PermissionRoute requiredModule="country">
                      <CountryPage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/state" 
                  element={
                    <PermissionRoute requiredModule="state">
                      <StatePage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/customers" 
                  element={
                    <PermissionRoute requiredModule="customers">
                      <TenantsPage/>
                    </PermissionRoute>
                  } 
                />
               
               <Route
                  path="/tenants" 
                  element={
                    <PermissionRoute requiredModule="tenants">
                      <TenantsPage />
                    </PermissionRoute>
                  } 
               />
               
             
                <Route 
                  path="/settings" 
                  element={
                    <PermissionRoute requiredModule="settings">
                      <SettingsPage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/language" 
                  element={
                    <PermissionRoute requiredModule="language">
                      <LanguagePage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/currency" 
                  element={
                    <PermissionRoute requiredModule="currency">
                      <CurrencyPage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/employees" 
                  element={
                    <PermissionRoute requiredModule="employees">
                      <EmployeePage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/contractors" 
                  element={
                    <PermissionRoute requiredModule="contractors">
                      <ContractorPage />
                    </PermissionRoute>
                  } 
                />

                <Route 
                  path="/assign-contractor" 
                  element={
                    <PermissionRoute requiredModule="assign-contractor">
                      <AssignContractorPage />
                    </PermissionRoute>
                  } 
                />

                 <Route 
                  path="/assign-vendor" 
                  element={
                    <PermissionRoute requiredModule="assign-vendor">
                      <AssignVendorPage />
                    </PermissionRoute>
                  } 
                />

                 <Route 
                  path="/assign-employee" 
                  element={
                    <PermissionRoute requiredModule="assign-employee">
                      <AssignEmployeePage />
                    </PermissionRoute>
                  } 
                />


                
              
                <Route 
                  path="/vendor-types" 
                  element={
                    <PermissionRoute requiredModule="vendor-type">
                      <VendorTypePage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/vendors" 
                  element={
                    <PermissionRoute requiredModule="vendor">
                      <VendorPage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/property-amenities" 
                  element={
                    <PermissionRoute requiredModule="property-amenity">
                      <PropertyAmenitiesPage />
                    </PermissionRoute>
                  } 
                />
                
                {/* Development/Testing routes - always accessible for now */}
                <Route path="/auth-test" element={<AuthTestPage />} />
                
                {/* Catch-all route for 404 */}
                <Route 
                  path="*" 
                  element={<PlaceholderPage title="Page Not Found" description="The page you're looking for doesn't exist." />} 
                />
              </Routes>
            </DashboardLayout>
          </Router>
            </AuthGate>
        </ThemeProvider>
        </TranslationProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}