import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import { Badge, Edit2, ToggleRight, Trash2, MoreHorizontal } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, } from '@/components/ui/dropdown-menu';

const RentTypesTable = ({
    rentType,
  handleEdit,
  handleToggleStatus,
  handleDelete,
}) => {
  return (
                    <Card key={rentType.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg text-gray-900 mb-2">
                          {rentType.name}
                        </h3>
                        {rentType.description && (
                          <p className="text-gray-600 text-sm line-clamp-2">
                            {rentType.description}
                          </p>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleEdit(rentType)}>
                            <Edit2 className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleToggleStatus(rentType)}>
                            <ToggleRight className="mr-2 h-4 w-4" />
                            {rentType.is_active ? 'Deactivate' : 'Activate'}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDelete(rentType)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <div className="flex justify-between items-center">
                      <Badge
                        variant="secondary"
                        className={rentType.is_active ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'}
                      >
                        {rentType.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                      <div className="text-sm text-gray-500">
                        Order: {rentType.sort_order}
                      </div>
                    </div>
                  </CardContent>
                </Card>
  )};
  export default RentTypesTable;