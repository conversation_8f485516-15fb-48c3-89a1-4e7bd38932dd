import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, Edit2, ToggleRight, ToggleLeft, Trash2 } from "lucide-react";
import propertyStatusAPI from '@/services/propertyStatusAPI';
import { showAlert } from '@/utils/sweetAlert';

const PropertyStatusTable = ({
  searchTerm = '',
  statusFilter = 'all',
  getIconComponent,
  openModal,
  handleToggleStatus,
  handleDelete,
}) => {
  const [propertyStatuses, setPropertyStatuses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current_page: 1,
    per_page: 15,
    total: 0,
    last_page: 1
  });

  // Fetch property statuses
  const fetchPropertyStatuses = async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        per_page: 15,
        search: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        include_projects_count: true
      };

      const response = await propertyStatusAPI.getAll(params);

      if (response.success) {
        setPropertyStatuses(response.data.data || []);
        setPagination({
          current_page: response.data.current_page || 1,
          per_page: response.data.per_page || 15,
          total: response.data.total || 0,
          last_page: response.data.last_page || 1
        });
      }
    } catch (error) {
      console.error('Error fetching property statuses:', error);
      showAlert('Error', 'Failed to fetch property statuses', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Initial load and refetch when search/filter changes
  useEffect(() => {
    fetchPropertyStatuses();
  }, [searchTerm, statusFilter]);

  // Filter statuses locally (additional client-side filtering if needed)
  const filteredStatuses = propertyStatuses.filter(status => {
    const matchesSearch = !searchTerm ||
      status.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      status.slug.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || status.status === statusFilter;
    return matchesSearch && matchesStatus;
  });
  return (
    <Card>
      <CardHeader>
        <CardTitle>Property Statuses ({filteredStatuses.length})</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {filteredStatuses.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredStatuses.map((status) => {
                  const IconComponent = getIconComponent(status.icon);
                  return (
                    <Card key={status.id} className="shadow-md border rounded-xl">
                      <CardHeader className="flex flex-row items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: status.color }}
                          ></div>
                          <IconComponent
                            className="h-5 w-5"
                            style={{ color: status.color }}
                          />
                          <span className="font-semibold">{status.name}</span>
                        </div>
                        <Badge
                          variant={status.status === "active" ? "default" : "secondary"}
                        >
                          {status.status}
                        </Badge>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        {status.description && (
                          <p className="text-sm text-gray-500">{status.description}</p>
                        )}
                        <div className="flex flex-wrap gap-2 text-sm">
                          <Badge variant="outline">
                            {status.projects_count || 0} projects
                          </Badge>
                          <code className="bg-gray-100 px-2 py-1 rounded">
                            {status.slug}
                          </code>
                          <span className="text-gray-600">
                            Order: {status.sort_order}
                          </span>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end gap-2 pt-3">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openModal("view", status)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openModal("edit", status)}
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleStatus(status)}
                          >
                            {status.status === "active" ? (
                              <ToggleRight className="h-4 w-4 text-green-600" />
                            ) : (
                              <ToggleLeft className="h-4 w-4 text-gray-400" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(status)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No property statuses found
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default PropertyStatusTable;
