const e={common:{app:{name:"Property Everywhere",title:"Property Everywhere",description:"Complete property management solution",shortName:"PropertyEverywhere",version:"1.0.0",tagline:"Manage properties with ease",company:"Property Everywhere Inc."},buttons:{add:"Add",edit:"Edit",delete:"Delete",save:"Save",cancel:"Cancel",confirm:"Confirm",close:"Close",search:"Search",filter:"Filter",export:"Export",import:"Import",refresh:"Refresh",loading:"Loading...",submit:"Submit",reset:"Reset",clear:"Clear",view:"View",download:"Download",upload:"Upload",continue:"Continue",back:"Back",next:"Next",previous:"Previous",finish:"Finish",notSet:"Not set"},loading:"Loading...",error:"Error",success:"Success",warning:"Warning",info:"Information",status:{active:"Active",inactive:"Inactive",pending:"Pending",approved:"Approved",rejected:"Rejected",completed:"Completed",draft:"Draft",published:"Published",processing:"Processing",failed:"Failed",success:"Success"},messages:{success:"Operation completed successfully",error:"An error occurred",warning:"Warning",info:"Information",loading:"Loading, please wait...",noData:"No data available",confirmDelete:"Are you sure you want to delete this item?",confirmAction:"Are you sure you want to perform this action?",saved:"Changes saved successfully",deleted:"Item deleted successfully",updated:"Item updated successfully",created:"Item created successfully",accessDenied:"Access denied",sessionExpired:"Session expired, please login again",networkError:"Network error, please check your connection"},navigation:{dashboard:"Dashboard",analytics:"Analytics",landOwners:"Land Owners",landAcquisition:"Land Acquisition",projects:"Properties",lifecycle:"Lifecycle",employees:"Employee Management",contractors:"Contractor Management",assignContractor:"Assign Contractor",assignVendor:"Assign Vendor",assignEmployee:"Assign Employee",vendorTypes:"Vendor Types",vendors:"Vendors",propertyAmenities:"Property Amenities",propertyTypes:"Property Types",propertyStatus:"Property Status",country:"Country",state:"State/Province",city:"City",language:"Language",currency:"Currency",customers:"Customers","unit-type":"Unit Type","property-stage":"Property Condition","property-service":"Property Service","property-services":"Property Service","rent-type":"Rent Type","rent-types":"Rent Type","lease-type":"Lease Type","lease-types":"Lease Type",invoices:"Invoice","payment-type":"Payment Type","payment-types":"Payment Type","payment-status":"Payment Status","payment-statuses":"Payment Status","payment-method":"Payment Method",invoice:"Invoice",tenants:"Tenants",roleManagement:"Role Management",settings:"Settings",documentsAdmin:"Documents & Admin",landDevelopment:"Land & Development",hrManagement:"Human Resources Management",developmentCosting:"Head Development Costing"},forms:{required:"This field is required",invalidEmail:"Please enter a valid email address",invalidPhone:"Please enter a valid phone number",passwordMismatch:"Passwords do not match",minLength:"Minimum length is {min} characters",maxLength:"Maximum length is {max} characters",selectOption:"Please select an option",invalidDate:"Please enter a valid date",invalidNumber:"Please enter a valid number"},table:{name:"Name",code:"Code",status:"Status",actions:"Actions",createdAt:"Created At",updatedAt:"Updated At",noRecords:"No records found",showing:"Showing {start} to {end} of {total} entries",previous:"Previous",next:"Next",rowsPerPage:"Rows per page"},auditLog:"Audit Log",loading:"Loading...",noData:"No data available"},dashboard:{title:"Dashboard",description:"Here's what's happening with your business today.",welcome:"Welcome back, {name}!",overview:"Overview",statistics:"Statistics",recentActivity:"Recent Activity",quickActions:"Quick Actions",viewReport:"View Report",statistics:{totalRevenue:"Total Revenue",subscriptions:"Subscriptions",sales:"Sales",activeNow:"Active Now",fromLastMonth:"from last month",fromLastHour:"from last hour"}},charts:{totalVisitors:"Total Visitors",totalVisitorsDescription:"Total for the last 3 months",mobile:"Mobile",desktop:"Desktop",periods:{week:"Week",month:"Month",quarter:"Quarter"}},auth:{welcomeBack:"Welcome back",loginCredentials:"Login with your credentials",email:"Email",password:"Password",forgotPassword:"Forgot your password?",login:"Login",signingIn:"Signing in...",loginFailed:"Login failed",loginFailedCredentials:"Login failed. Please check your credentials.",orUseQuickAccess:"Or use quick access",quickAccess:"Quick Access (Demo Accounts)",roles:{superAdmin:"Super Admin",admin:"Admin",manager:"Manager",editor:"Editor"},descriptions:{superAdmin:"Full system access",admin:"Administrative access",manager:"Management access",editor:"Editor access"},termsAndPrivacy:{byClicking:"By clicking continue, you agree to our",termsOfService:"Terms of Service",and:"and",privacyPolicy:"Privacy Policy"}},unitTypes:{title:"Unit Types",addNew:"Add New Unit Type",editType:"Edit Unit Type",typeName:"Type Name",totalSize:"Total Size (sqft)",flatSize:"Flat Size",bedrooms:"Bedrooms",bathrooms:"Bathrooms",basePrice:"Base Price",autoGenerate:"Auto Generate Units",isActive:"Is Active",fetchError:"Failed to fetch unit types",saveError:"Failed to save unit type",deleteError:"Failed to delete unit type",saveSuccess:"Unit type saved successfully",deleteSuccess:"Unit type deleted successfully",confirmDelete:"Are you sure you want to delete this unit type?",noTypes:"No unit types found"},ui:{modal:{close:"Close",save:"Save",cancel:"Cancel"},unitTypes:{title:"Unit Types Management",addNew:"Add New Unit Type",edit:"Edit Unit Type",delete:"Delete Unit Type",noData:"No unit types found",fetchError:"Failed to fetch unit types",saveError:"Failed to save unit type",deleteConfirm:"Are you sure you want to delete this unit type?"}},language:{title:"Language Management",addLanguage:"Add Language",editLanguage:"Edit Language",default:"Default",current:"Current",select:"Select",fields:{name:"Name",code:"Code",nativeName:"Native Name",flag:"Flag (emoji)",direction:"Direction",status:"Status",isDefault:"Set as default language"},direction:{ltr:"Left to Right (LTR)",rtl:"Right to Left (RTL)"},statistics:{totalLanguages:"Total Languages",activeLanguages:"Active Languages",defaultLanguage:"Default Language",ltrLanguages:"LTR Languages",rtlLanguages:"RTL Languages"},messages:{setDefault:"Set as Default Language?",setDefaultText:"This will make this language the default for the system",setDefaultConfirm:"Yes, set as default!",defaultUpdated:"Default language updated successfully",onlyActiveCanBeDefault:"Only active languages can be set as default"}},currency:{title:"Currency Management",addCurrency:"Add Currency",editCurrency:"Edit Currency",fields:{name:"Name",code:"Code",symbol:"Symbol",exchangeRate:"Exchange Rate",isDefault:"Set as default currency",isActive:"Active"},statistics:{totalCurrencies:"Total Currencies",activeCurrencies:"Active Currencies",defaultCurrency:"Default Currency",averageRate:"Average Rate"},messages:{setDefault:"Set as Default Currency?",setDefaultText:"This will make this currency the default for the system",setDefaultConfirm:"Yes, set as default!",defaultUpdated:"Default currency updated successfully"}},landOwners:{title:"Land Owners",description:"Manage land owner records and information",searchTitle:"Search Land Owners",searchPlaceholder:"Search by name, father's name, phone, NID number, or email...",addOwner:"Add New Owner",editOwner:"Edit Land Owner",header:{title:"Land Owners",description:"Manage land owner records and information"},table:{serialNumber:"S.N",photo:"Photo",firstName:"First Name",lastName:"Last Name",fatherName:"Father's Name",nidNumber:"NID Number",contact:"Contact",address:"Address",status:"Status",documents:"Documents",actions:"Actions",noImage:"No Image",active:"Active",inactive:"Inactive",activate:"Activate",deactivate:"Deactivate",edit:"Edit",delete:"Delete"},forms:{addTitle:"Add New Land Owner",editTitle:"Edit Land Owner",addDescription:"Enter the land owner information.",editDescription:"Update the land owner information.",basicInfo:"Basic Information",firstNameLabel:"First Name",firstNamePlaceholder:"First Name",lastNameLabel:"Last Name",lastNamePlaceholder:"Last Name",fatherNameLabel:"Father's Name",fatherNamePlaceholder:"Land Owner's Father's Name",motherNameLabel:"Mother's Name",motherNamePlaceholder:"Land Owner's Mother's Name",addressLabel:"Address",addressPlaceholder:"Full Address (House 123, Road 456, Dhanmondi, Dhaka)",phoneLabel:"Phone",phonePlaceholder:"Land Owner's Phone Number",nidLabel:"NID/Passport Number",nidPlaceholder:"Land Owner's NID/Passport Number",emailLabel:"Email",emailPlaceholder:"<EMAIL>",required:"*",cancel:"Cancel",create:"Create Owner",update:"Update Owner",creating:"Creating...",updating:"Updating..."},fields:{fullName:"Full Name",firstName:"First Name",lastName:"Last Name",fatherName:"Father's Name",motherName:"Mother's Name",email:"Email",phone:"Phone",address:"Address",city:"City",state:"State",zipCode:"Zip Code",country:"Country",dateOfBirth:"Date of Birth",nationalId:"National ID",nidNumber:"NID Number",status:"Status",photo:"Photo",documentType:"Document Type",nidFront:"NID Front",nidBack:"NID Back",passportPhoto:"Passport Photo"},statistics:{totalOwners:"Total Owners",activeOwners:"Active Owners",newThisMonth:"New This Month",totalLands:"Total Lands"},stats:{activeLandOwners:"Active Land Owners",inactiveOwners:"Inactive Owners",recentRegistrations:"Recent Registrations",descriptions:{totalOwners:"Total registered land owners",activeOwners:"Currently active owners",inactiveOwners:"Inactive land owners",recentRegistrations:"New owners in last 30 days"}},auditLog:{title:"Land Owners Activity Log",description:"Complete audit trail of all land owner operations in the system",noLogs:"No audit logs found",noActivity:"No activity has been recorded yet.",dateTime:"Date & Time",action:"Action",landOwner:"Land Owner",user:"User",details:"Details",changes:"Changes:",close:"Close"},messages:{confirmDelete:"Are you sure you want to delete this land owner?",deleteSuccess:"Land owner deleted successfully",createSuccess:"Land owner created successfully",updateSuccess:"Land owner updated successfully",loadError:"Failed to load land owners. Please try again.",deleteError:"Failed to delete land owner. Please try again.",createError:"Failed to create land owner. Please try again.",updateError:"Failed to update land owner. Please try again."}},landAcquisition:{title:"Land Acquisition",addAcquisition:"Add Land Acquisition",editAcquisition:"Edit Land Acquisition",fields:{landSize:"Land Size",location:"Location",price:"Price",acquisitionDate:"Acquisition Date",status:"Status",description:"Description",documents:"Documents"},statistics:{totalAcquisitions:"Total Acquisitions",completedDeals:"Completed Deals",totalValue:"Total Value",averageSize:"Average Size"}},properties:{title:"Properties",description:"Manage your property portfolio",addProperty:"Add Property",editProperty:"Edit Property",viewProperty:"View Property",searchPlaceholder:"Search properties...",noLandAssigned:"No land assigned",allRegisteredProperties:"All registered projects",currentlyInProgress:"Currently in progress",successfullyFinished:"Successfully finished",totalProjectInvestments:"Total project investments",header:{title:"Properties",description:"Manage your property development projects"},table:{serialNumber:"S.N",image:"Image",name:"Property Name",type:"Type",status:"Status",location:"Location",startDate:"Start Date",endDate:"End Date",budget:"Budget",progress:"Progress",actions:"Actions",noImage:"No Image",edit:"Edit",delete:"Delete",view:"View",generateBrochure:"Generate Brochure",viewMasterplan:"View Masterplan",viewApprovalDocument:"View Approval Document",editProject:"Edit Project",unitDetails:"Unit Details",addNewProject:"Add New Project",updateProject:"Update Project",createProject:"Create Project"},forms:{addTitle:"Add New Property",editTitle:"Edit Property",addDescription:"Create a new property development project.",editDescription:"Update property project information.",basicInfo:"Basic Information",projectName:"Project Name",enterProjectName:"Enter project name",nameLabel:"Property Name",namePlaceholder:"Enter project name",typeLabel:"Property Type",typePlaceholder:"Select property type",statusLabel:"Status",statusPlaceholder:"Select status",locationLabel:"Location",locationPlaceholder:"Property location",startDateLabel:"Start Date",endDateLabel:"Expected End Date",budgetLabel:"Budget",budgetPlaceholder:"Enter total budget",description:"Description",descriptionLabel:"Description",enterProjectDescription:"Enter project description",descriptionPlaceholder:"Enter project description",addProjectAddressDetails:"Add project address details",timelineBudget:"Timeline & Budget",budgetLabel:"Total Budget",documentsLabel:"Documents",imageLabel:"Property Image",countryPlaceholder:"Search or select country...",statePlaceholder:"Search or select state/province...",cityPlaceholder:"Enter city name",citySelectFirst:"Please select a country first",addressPlaceholder:"Enter complete address including street, area, postal code, etc.",totalSftLabel:"Total SFT",totalSftPlaceholder:"Total square feet",floorsLabel:"Floors",floorsPlaceholder:"Number of floors",unitsPerFloorLabel:"Units per Floor",unitsPerFloorPlaceholder:"Units per floor",totalUnitsLabel:"Total Units",totalUnitsPlaceholder:"Total units",parkingLabel:"Parking Spaces",parkingPlaceholder:"Total parking spaces",unitTypesLabel:"Unit Types",unitTypesPlaceholder:"Select number of unit types",includeAddressLabel:"Include Land Address",required:"*",cancel:"Cancel",create:"Create Property",update:"Update Property",creating:"Creating...",updating:"Updating..."},filters:{allTypes:"All Types",allStatus:"All Status",selectType:"Select Type",selectStatus:"Select Status"},fields:{name:"Property Name",type:"Type",status:"Status",location:"Location",startDate:"Start Date",endDate:"End Date",budget:"Budget",description:"Description",documents:"Documents",image:"Image",progress:"Progress (%)",landAcquisition:"Land Acquisition"},types:{residential:"Residential",commercial:"Commercial",mixed:"Mixed Use",industrial:"Industrial",retail:"Retail",office:"Office"},statuses:{planning:"Planning",approved:"Approved",foundation:"Foundation",structure:"Structure",finishing:"Finishing",completed:"Completed",finished:"Finished",handover:"Handover",cancelled:"Cancelled",onHold:"On Hold"},statistics:{totalProperties:"Total Properties",activeProperties:"Active Properties",completedProperties:"Completed Properties",planningProperties:"Planning Stage",ongoingProperties:"Ongoing Properties",commercialProperties:"Commercial Properties",residentialProperties:"Residential Properties",mixedProperties:"Mixed Use Properties",totalBudget:"Total Budget",averageBudget:"Average Budget",propertiesByStatus:"Properties by Status",propertiesByType:"Properties by Type"},messages:{confirmDelete:"Are you sure you want to delete this property?",deleteSuccess:"Property deleted successfully",createSuccess:"Property created successfully",updateSuccess:"Property updated successfully",brochureSuccess:"Brochure generated successfully",loadError:"Failed to load properties. Please try again.",deleteError:"Failed to delete property. Please try again.",createError:"Failed to create property. Please try again.",updateError:"Failed to update property. Please try again.",brochureError:"Failed to generate brochure. Please try again."}},units:{title:"Unit Details",description:"Manage unit details for this project",addUnit:"Add Unit",editUnit:"Edit Unit",addNewUnit:"Add New Unit",updateUnit:"Update Unit",addUnitType:"Add Unit Type",unitName:"Unit Name",unitDetails:"Unit Details",unitTypes:"Unit Types",noUnitsFound:"No units found for this project",refreshData:"Refresh data",fields:{unitNumber:"Unit Number",unitNumberPlaceholder:"e.g., A1, Penthouse 1",unitType:"Unit Type",selectUnitType:"Select Unit Type",floor:"Floor",status:"Status",price:"Price",area:"Area",bedrooms:"Bedrooms",bathrooms:"Bathrooms",description:"Description"},types:{studio:"Studio",oneBedroom:"1 Bedroom",twoBedroom:"2 Bedroom",threeBedroom:"3 Bedroom",penthouse:"Penthouse",typeName:"Type Name",typeNamePlaceholder:"e.g., Studio, 1BR, 2BR, Penthouse",totalArea:"Total Area (sqft)",totalAreaPlaceholder:"e.g., 850",builtupArea:"Builtup Area (sqft)",builtupAreaPlaceholder:"e.g., 750",bedroomsPlaceholder:"e.g., 2",bathroomsPlaceholder:"e.g., 2",basePricePlaceholder:"Base price for this type",active:"Active"},statuses:{available:"Available",sold:"Sold",reserved:"Reserved",underConstruction:"Under Construction"},actions:{editUnit:"Edit Unit",previewDetails:"Preview Unit Type Details",deleteUnit:"Delete Unit"}},documents:{title:"Documents",viewer:"Document Viewer",identityDocuments:"Identity Documents",landDocuments:"Land Documents",existingDocuments:"Existing Documents",noDocuments:"No documents added yet",existing:"Existing",new:"New",upload:{nidFront:"Upload NID front side",nidBack:"Upload NID back side",passport:"Upload passport information page",documentFile:"Upload document file (PDF, DOC, Images)",placeholder:"Upload document file (PDF, DOC, Images)"},fields:{title:"Title",titlePlaceholder:"e.g., Land Deed, Survey Report, etc.",description:"Description",descriptionPlaceholder:"Brief description of the document...",documentFile:"Document File"},actions:{zoomOut:"Zoom Out",zoomIn:"Zoom In",rotate:"Rotate",openNewTab:"Open in new tab",download:"Download",close:"Close",viewDocument:'Click "Open in new tab" to view the document'}},pages:{lifecycle:{title:"Lifecycle",description:"Lifecycle management and tracking"},customers:{title:"Customers",description:"Customer management functionality coming soon"},reports:{title:"Reports",description:"Generate and view detailed reports"},wordAssistant:{title:"Word Assistant",description:"AI-powered document assistance"},more:{title:"More",description:"Additional features and tools"},notFound:{title:"Page Not Found",description:"The page you're looking for doesn't exist."}},debug:{title:"API Debug Panel",environment:"Environment Information",apiConfig:"API Configuration",connectivityTests:"Connectivity Tests",usageInstructions:"Usage Instructions"},notifications:{title:"Notifications",markAllRead:"Mark all as read",noNotifications:"No notifications",viewAll:"View all notifications"},tables:{headers:{header:"Header",sectionType:"Section Type",status:"Status",target:"Target",limit:"Limit",reviewer:"Reviewer"},actions:{edit:"Edit",duplicate:"Duplicate",delete:"Delete",resetToDefault:"Reset to Default"},pagination:{rowsPerPage:"Rows per page"}},users:{title:"User Management",profile:"Profile",fields:{firstName:"First Name",lastName:"Last Name",email:"Email",phone:"Phone",role:"Role",company:"Company",bio:"Bio",timezone:"Timezone",language:"Language"},messages:{profileUpdated:"Profile updated successfully",passwordChanged:"Password changed successfully"}},roles:{title:"Role Management",addRole:"Add Role",editRole:"Edit Role",fields:{name:"Name",description:"Description",permissions:"Permissions",modules:"Accessible Modules"},permissions:{create:"Create",read:"Read",update:"Update",delete:"Delete",export:"Export",manage:"Manage",use:"Use"}},settings:{title:"Settings",general:"General Settings",profile:"Profile Settings",security:"Security Settings",notifications:"Notification Settings",language:"Language & Localization",languageDescription:"Choose your preferred language",appearance:"Appearance",system:"System Settings"},auth:{signIn:"Sign In",signUp:"Sign Up",login:"Login",logout:"Logout",register:"Register",email:"Email Address",password:"Password",enterEmail:"Enter your email",enterPassword:"Enter your password",forgotPassword:"Forgot Password",resetPassword:"Reset Password",changePassword:"Change Password",currentPassword:"Current Password",newPassword:"New Password",confirmPassword:"Confirm Password",rememberMe:"Remember Me",welcomeBack:"Welcome back! Please enter your details.",signingIn:"Signing in...",noAccount:"Don't have an account?",demoAccounts:"Demo Accounts",clickToAutoFill:"Click any account below to auto-fill the login form",note:"Note",demoNote:"These are demo accounts for testing purposes. Use the credentials to explore different user roles.",loginSuccess:"Login successful",logoutSuccess:"Logout successful",invalidCredentials:"Invalid email or password",sessionExpired:"Your session has expired. Please login again."},date:{today:"Today",yesterday:"Yesterday",tomorrow:"Tomorrow",thisWeek:"This Week",thisMonth:"This Month",thisYear:"This Year",lastWeek:"Last Week",lastMonth:"Last Month",lastYear:"Last Year",formats:{short:"MM/DD/YYYY",medium:"MMM DD, YYYY",long:"MMMM DD, YYYY",full:"dddd, MMMM DD, YYYY"}},upload:{selectFile:"Select File",dropFile:"Drop file here",uploading:"Uploading...",uploadSuccess:"File uploaded successfully",uploadError:"Upload failed",invalidFileType:"Invalid file type",fileTooLarge:"File too large",maxSize:"Maximum file size: {size}MB"},country:{title:"Country Management",description:"Manage countries and their information in the system",searchTitle:"Search Countries",searchPlaceholder:"Search by name, code, or continent...",addCountry:"Add Country",editCountry:"Edit Country",fields:{name:"Name",code:"Code",isoCode:"ISO Code",capital:"Capital",currency:"Currency",phoneCode:"Phone Code",continent:"Continent",population:"Population",status:"Status"},statistics:{totalCountries:"Total Countries",activeCountries:"Active Countries",inactiveCountries:"Inactive Countries"},messages:{confirmDelete:"Are you sure you want to delete this country?",deleteSuccess:"Country deleted successfully",createSuccess:"Country created successfully",updateSuccess:"Country updated successfully",loadError:"Failed to load countries. Please try again.",deleteError:"Failed to delete country. Please try again.",createError:"Failed to create country. Please try again.",updateError:"Failed to update country. Please try again."}},state:{title:"State/Province Management",description:"Manage states, provinces, and administrative divisions",searchTitle:"Search States/Provinces",searchPlaceholder:"Search by name, code, country, or capital...",addState:"Add State/Province",editState:"Edit State/Province",fields:{name:"Name",code:"Code",type:"Type",country:"Country",capital:"Capital",timezone:"Timezone",latitude:"Latitude",longitude:"Longitude",description:"Description",status:"Status"},types:{state:"State",province:"Province",territory:"Territory",region:"Region",district:"District",federal_district:"Federal District"},statistics:{totalStates:"Total States",activeStates:"Active States",inactiveStates:"Inactive States",statesByCountry:"States by Country",statesByType:"States by Type"},messages:{confirmDelete:"Are you sure you want to delete this state/province?",deleteSuccess:"State/Province deleted successfully",createSuccess:"State/Province created successfully",updateSuccess:"State/Province updated successfully",loadError:"Failed to load states/provinces. Please try again.",deleteError:"Failed to delete state/province. Please try again.",createError:"Failed to create state/province. Please try again.",updateError:"Failed to update state/province. Please try again.",selectCountry:"Please select a country first",duplicateName:"A state with this name already exists in the selected country"}},city:{title:"City Management",description:"Manage cities and their information",searchTitle:"Search Cities",searchPlaceholder:"Search by name, code, district, state, or country...",addCity:"Add City",editCity:"Edit City",viewCity:"City Details",fields:{name:"City Name",code:"Code",type:"Type",state:"State/Province",country:"Country",district:"District",postalCode:"Postal Code",timezone:"Timezone",latitude:"Latitude",longitude:"Longitude",elevation:"Elevation",population:"Population",area:"Area (km²)",description:"Description",status:"Status"},types:{city:"City",town:"Town",village:"Village",municipality:"Municipality",district:"District",borough:"Borough",township:"Township"},statistics:{totalCities:"Total Cities",activeCities:"Active Cities",inactiveCities:"Inactive Cities",citiesByState:"Cities by State",citiesByCountry:"Cities by Country",citiesByType:"Cities by Type"},messages:{confirmDelete:"Are you sure you want to delete this city?",deleteSuccess:"City deleted successfully",createSuccess:"City created successfully",updateSuccess:"City updated successfully",loadError:"Failed to load cities. Please try again.",deleteError:"Failed to delete city. Please try again.",createError:"Failed to create city. Please try again.",updateError:"Failed to update city. Please try again.",selectCountry:"Please select a country first",selectState:"Please select a state first",duplicateName:"A city with this name already exists in the selected state",noCitiesFound:"No cities found",testApiSuccess:"API test successful! Check console for details.",testApiFailed:"API test failed! Check console for details.",testDropdownSuccess:"Dropdown test successful! Check console for details.",testDropdownFailed:"Dropdown test failed! Check console for details."}},cities:{title:"City Management",subtitle:"Manage cities and their information",add:"Add City",edit:"Edit City",view:"View City Details",searchPlaceholder:"Search cities...",noDataMessage:"No cities found",noDataSubMessage:"Get started by adding your first city",filterAdjustMessage:"Try adjusting your search or filters",fields:{name:"City Name",code:"City Code",type:"Type",district:"District",state:"State/Province",country:"Country",postalCode:"Postal Code",population:"Population",area:"Area (km²)",timezone:"Timezone",latitude:"Latitude",longitude:"Longitude",elevation:"Elevation",description:"Description",status:"Status"},types:{city:"City",town:"Town",village:"Village",municipality:"Municipality",district:"District",borough:"Borough",township:"Township"},statistics:{totalCities:"Total Cities",activeCities:"Active Cities",inactiveCities:"Inactive Cities",citiesByState:"Cities by State",citiesByCountry:"Cities by Country",citiesByType:"Cities by Type"},messages:{confirmDelete:"Are you sure you want to delete this city?",deleteSuccess:"City deleted successfully",createSuccess:"City created successfully",updateSuccess:"City updated successfully",loadError:"Failed to load cities. Please try again.",deleteError:"Failed to delete city. Please try again.",createError:"Failed to create city. Please try again.",updateError:"Failed to update city. Please try again.",selectState:"Please select a state first",selectCountry:"Please select a country first",duplicateName:"A city with this name already exists in the selected state"}},auth:{login:{title:"Sign In",subtitle:"Welcome back! Please sign in to your account.",email:"Email Address",password:"Password",rememberMe:"Remember me",forgotPassword:"Forgot password?",signIn:"Sign In",signUp:"Sign Up",noAccount:"Don't have an account?",createAccount:"Create one here"},logout:{title:"Sign Out",message:"Are you sure you want to sign out of your account?",confirm:"Yes, sign out",cancel:"Cancel",signingOut:"Signing out...",success:"Signed Out",successMessage:"You have been successfully signed out",error:"Sign Out Failed",errorMessage:"There was an error signing you out. Please try again."},register:{title:"Create Account",subtitle:"Create your account to get started.",firstName:"First Name",lastName:"Last Name",email:"Email Address",password:"Password",confirmPassword:"Confirm Password",agreeTerms:"I agree to the Terms and Conditions",createAccount:"Create Account",haveAccount:"Already have an account?",signIn:"Sign in here"},messages:{loginSuccess:"Login successful! Welcome back.",loginError:"Invalid credentials. Please try again.",registerSuccess:"Account created successfully! Please sign in.",registerError:"Failed to create account. Please try again.",logoutSuccess:"You have been logged out successfully.",sessionExpired:"Your session has expired. Please sign in again.",accessDenied:"Access denied. You don't have permission to access this resource."}},alerts:{titles:{success:"Success!",error:"Error!",warning:"Warning!",info:"Information",confirm:"Confirm Action",delete:"Delete Item",accessAdministrative:"Access Administrative Area"},messages:{confirmDelete:"Are you sure you want to delete this item? This action cannot be undone.",confirmAction:"Are you sure you want to perform this action?",accessSensitive:"You are about to access {area}. This area contains sensitive system settings. Are you sure you want to continue?",unsavedChanges:"You have unsaved changes. Are you sure you want to leave this page?",networkError:"Network error occurred. Please check your internet connection.",serverError:"Server error occurred. Please try again later.",validationError:"Please check the form for errors and try again."},buttons:{yes:"Yes",no:"No",ok:"OK",cancel:"Cancel",continue:"Yes, continue",delete:"Yes, delete",save:"Save Changes",discard:"Discard Changes"}},ui:{headers:{welcome:"Welcome back, {name}!",overview:"System Overview",recentActivity:"Recent Activity",quickActions:"Quick Actions",statistics:"Statistics",analytics:"Analytics Dashboard",reports:"Reports & Analytics",defaultUser:"User",profile:"Profile",billing:"Billing",team:"Team",account:"Account",preferences:"Preferences",support:"Support",help:"Help",documentation:"Documentation"},cards:{total:"Total",active:"Active",inactive:"Inactive",pending:"Pending",completed:"Completed",thisMonth:"This Month",lastMonth:"Last Month",thisYear:"This Year",growth:"Growth",change:"Change",trend:"Trend",viewAll:"View All",viewDetails:"View Details",manage:"Manage",addNew:"Add New"},pagination:{showing:"Showing {start} to {end} of {total} entries",previous:"Previous",next:"Next",first:"First",last:"Last",page:"Page",of:"of",rowsPerPage:"Rows per page",noData:"No data to display",noResults:"No results found"},search:{placeholder:"Search...",noResults:"No results found",searchFor:"Search for {term}",clearSearch:"Clear search",filterBy:"Filter by",sortBy:"Sort by",ascending:"Ascending",descending:"Descending",searchInProgress:"Searching...",searchHint:"Type to search",advancedSearch:"Advanced Search",quickFilters:"Quick Filters"},loading:{default:"Loading...",saving:"Saving...",deleting:"Deleting...",uploading:"Uploading...",processing:"Processing...",fetching:"Fetching data...",please_wait:"Please wait...",initializing:"Initializing...",connecting:"Connecting...",syncing:"Syncing..."},notifications:{title:"Notifications",markAllRead:"Mark all as read",viewAll:"View all notifications",noNotifications:"No new notifications",settings:"Notification Settings",clear:"Clear notifications"},theme:{light:"Light",dark:"Dark",system:"System",toggle:"Toggle theme"}},datetime:{today:"Today",yesterday:"Yesterday",tomorrow:"Tomorrow",thisWeek:"This Week",lastWeek:"Last Week",thisMonth:"This Month",lastMonth:"Last Month",thisYear:"This Year",lastYear:"Last Year",formats:{date:"YYYY-MM-DD",time:"HH:mm",datetime:"YYYY-MM-DD HH:mm",full:"dddd, MMMM Do YYYY, h:mm:ss a"},relative:{now:"just now",secondsAgo:"{count} seconds ago",minuteAgo:"a minute ago",minutesAgo:"{count} minutes ago",hourAgo:"an hour ago",hoursAgo:"{count} hours ago",dayAgo:"a day ago",daysAgo:"{count} days ago",weekAgo:"a week ago",weeksAgo:"{count} weeks ago",monthAgo:"a month ago",monthsAgo:"{count} months ago",yearAgo:"a year ago",yearsAgo:"{count} years ago"}},files:{upload:"Upload File",download:"Download",delete:"Delete File",preview:"Preview",select:"Select File",dropZone:"Drop files here or click to select",maxSize:"Maximum file size: {size}",allowedTypes:"Allowed types: {types}",uploading:"Uploading...",uploadSuccess:"File uploaded successfully",uploadError:"Failed to upload file",deleteConfirm:"Are you sure you want to delete this file?",deleteSuccess:"File deleted successfully",deleteError:"Failed to delete file",invalidType:"Invalid file type",tooLarge:"File is too large",noFile:"No file selected"}};export{e as default};
