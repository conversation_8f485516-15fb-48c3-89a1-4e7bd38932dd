import React from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {Package,X  } from "lucide-react";
import { Switch } from "@/components/ui/switch";
const PropertyAmenitiesModal = ({
 
  saving,
  handleSave,
  isEditDialogOpen,
  formData,
  setFormData,
  formErrors,
  categories,
  icons,
  getCategoryIcon,
  resetForm,
  setIsCreateDialogOpen,
  setIsEditDialogOpen
}) => {
  return (
       <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
              <div className="flex items-center space-x-3">
                <Package className="h-6 w-6" />
                <div>
                  <h3 className="text-lg font-semibold">
                    {isEditDialogOpen ? 'Edit Amenity' : 'Create New Amenity'}
                  </h3>
                  <p className="text-blue-100 text-sm">
                    {isEditDialogOpen 
                      ? 'Update the amenity information below.'
                      : 'Fill in the details to create a new property amenity.'
                    }
                  </p>
                </div>
              </div>
              <button
                onClick={() => {
                  setIsCreateDialogOpen(false);
                  setIsEditDialogOpen(false);
                  resetForm();
                }}
                className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <form onSubmit={handleSave} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="no-focus-outline">
                    <Label htmlFor="name">Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter amenity name"
                      className={formErrors.name ? 'border-destructive' : ''}
                    />
                    {formErrors.name && (
                      <p className="text-sm text-destructive mt-1">{formErrors.name[0]}</p>
                    )}
                  </div>
                  
                  <div className="no-focus-outline">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter amenity description"
                      rows={3}
                      className={formErrors.description ? 'border-destructive' : ''}
                    />
                    {formErrors.description && (
                      <p className="text-sm text-destructive mt-1">{formErrors.description[0]}</p>
                    )}
                  </div>

                  <div className='no-focus-outline'>
                    <Label htmlFor="category">Category *</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => setFormData({ ...formData, category: value })}
                    >
                      <SelectTrigger className={formErrors.category ? 'border-destructive' : ''}>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(categories).map(([key, label]) => (
                          <SelectItem key={key} value={key}>
                            <div className="flex items-center gap-2">
                              {getCategoryIcon(key)}
                              {label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formErrors.category && (
                      <p className="text-sm text-destructive mt-1">{formErrors.category[0]}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="sort_order">Sort Order</Label>
                    <Input
                      id="sort_order"
                      type="number"
                      min="0"
                      value={formData.sort_order}
                      onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
                      placeholder="0"
                      className={formErrors.sort_order ? 'border-destructive' : ''}
                    />
                    {formErrors.sort_order && (
                      <p className="text-sm text-destructive mt-1">{formErrors.sort_order[0]}</p>
                    )}
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor="icon">Icon</Label>
                    <Select
                      value={formData.icon || "no-icon"}
                      onValueChange={(value) => setFormData({ ...formData, icon: value === "no-icon" ? "" : value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select icon (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="no-icon">No Icon</SelectItem>
                        {Object.entries(icons).map(([key, label]) => (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="col-span-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="is_active"
                        checked={formData.is_active}
                        onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                      />
                      <Label htmlFor="is_active">Active</Label>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsCreateDialogOpen(false);
                      setIsEditDialogOpen(false);
                      resetForm();
                    }}
                    disabled={saving}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={saving}
                    className="bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    {saving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    {isEditDialogOpen ? 'Update' : 'Create'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
  )};
export default PropertyAmenitiesModal;
