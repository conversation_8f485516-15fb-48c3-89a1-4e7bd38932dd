<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RentType;
use Illuminate\Http\JsonResponse;
class RentTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try {
            $query = RentType::query();
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where('name', 'LIKE', "%{$search}%");
            }
            $perPage = $request->get('per_page', 10);
            $rentTypes = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $rentTypes
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve rent types: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:rent_types,name',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $rentType = RentType::create($validated);
            return response()->json([
                'success' => true,
                'data' => $rentType,
                'message' => 'Rent type created successfully'
            ], 201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to create rent type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id):jsonResponse
    {
        //
        try{
            $rentType = RentType::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $rentType,
                'message' => 'Rent type retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve rent type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id):JsonResponse
    {
        //
        try{
            $rentType = RentType::findOrFail($id);
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:rent_types,name,' . $id,
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $rentType->update($validated);
            return response()->json([
                'success' => true,
                'data' => $rentType->fresh(),
                'message' => 'Rent type updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update rent type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id):JsonResponse
    {
        //
        try{
            $rentType = RentType::findOrFail($id);
            $rentType->delete();
            return response()->json([
                'success' => true,
                'message' => 'Rent type deleted successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete rent type: ' . $e->getMessage()
            ], 500);
        }
    }

    public function dropdown():JsonResponse
    {
        try{
            $rentTypes = RentType::active()
                ->ordered()
                ->select('id', 'name', 'description')
                ->get();
            return response()->json([
                'success' => true,
                'data' => $rentTypes,
                'message' => 'Rent types for dropdown retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve rent types for dropdown',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function toggleStatus(string $id):JsonResponse
    {
        try{    
            $rentType = RentType::findOrFail($id);
            $rentType->is_active = !$rentType->is_active;
            $rentType->save();
            return response()->json([
                'success' => true,
                'data' => $rentType,
                'message' => 'Rent type status updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update rent type status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    
}
