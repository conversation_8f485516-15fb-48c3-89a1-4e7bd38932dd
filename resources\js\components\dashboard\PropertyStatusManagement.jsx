import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import PropertyStatusTable from './PropertyStatusTable';
import PropertyStatusSearch from './PropertyStatusSearch';
import PropertyStatusModal from './PropertyStatusModal';
import { 
  Plus, FileText, Package,
  CheckCircle, AlertCircle, Clock, DollarSign, Key, Wrench
} from 'lucide-react';
import { showAlert, showAlertMethods as showConfirmation } from '@/utils/sweetAlert';
import propertyStatusAPI from '@/services/propertyStatusAPI';

const PropertyStatusManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    color: '#3B82F6',
    icon: '',
    status: 'active',
    sort_order: 0,
    metadata: {}
  });

  // Icon options
  const iconOptions = [
    { value: 'ClipboardList', label: 'Planning', icon: Clock },
    { value: 'Hammer', label: 'Construction', icon: AlertCircle },
    { value: 'CheckCircle', label: 'Completed', icon: CheckCircle },
    { value: 'DollarSign', label: 'Sold', icon: DollarSign },
    { value: 'Key', label: 'Rented', icon: Key },
    { value: 'Wrench', label: 'Maintenance', icon: Wrench },
    { value: 'Package', label: 'Package', icon: Package },
    { value: 'FileText', label: 'Document', icon: FileText },
  ];

  // Color options
  const colorOptions = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', 
    '#6B7280', '#F97316', '#EC4899', '#14B8A6', '#84CC16'
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Auto-generate slug when name changes
      if (field === 'name' && !prev.slug) {
        updated.slug = value.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '_')
          .replace(/-+/g, '_');
      }
      
      return updated;
    });
  };

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      color: '#3B82F6',
      icon: '',
      status: 'active',
      sort_order: 0,
      metadata: {}
    });
    setSelectedStatus(null);
  };

  const openModal = (mode, status = null) => {
    setModalMode(mode);
    setSelectedStatus(status);
    
    if (status && (mode === 'edit' || mode === 'view')) {
      setFormData({
        name: status.name || '',
        slug: status.slug || '',
        description: status.description || '',
        color: status.color || '#3B82F6',
        icon: status.icon || '',
        status: status.status || 'active',
        sort_order: status.sort_order || 0,
        metadata: status.metadata || {}
      });
    } else {
      resetForm();
    }
    
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    resetForm();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      let response;
      const submitData = { ...formData };

      if (modalMode === 'create') {
        response = await propertyStatusAPI.create(submitData);
      } else {
        response = await propertyStatusAPI.update(selectedStatus.id, submitData);
      }

      if (response.success) {
        showAlert(
          'Success',
          `Property status ${modalMode === 'create' ? 'created' : 'updated'} successfully!`,
          'success'
        );
        closeModal();
        // Table will refetch automatically due to prop changes
      }
    } catch (error) {
      console.error('Error saving property status:', error);
      showAlert('Error', error.message || 'Failed to save property status', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (status) => {
    try {
      const newStatus = status.status === 'active' ? 'inactive' : 'active';
      const result = await showConfirmation.confirm(
        'Toggle Status',
        `Are you sure you want to ${newStatus === 'active' ? 'activate' : 'deactivate'} "${status.name}"?`,
        'Yes, do it!',
        'Cancel'
      );

      if (result.isConfirmed) {
        const response = await propertyStatusAPI.toggleStatus(status.id);
        if (response.success) {
          showAlert('Success', `Property status ${newStatus} successfully!`, 'success');
          // Table will refetch automatically due to prop changes
        }
      }
    } catch (error) {
      console.error('Error toggling status:', error);
      showAlert('Error', 'Failed to toggle status', 'error');
    }
  };

  const handleDelete = async (status) => {
    try {
      const result = await showConfirmation.confirmDelete(status.name);

      if (result.isConfirmed) {
        const response = await propertyStatusAPI.delete(status.id);
        if (response.success) {
          showAlert('Success', 'Property status deleted successfully!', 'success');
          // Table will refetch automatically due to prop changes
        }
      }
    } catch (error) {
      console.error('Error deleting property status:', error);
      showAlert('Error', error.message || 'Failed to delete property status', 'error');
    }
  };

  const getIconComponent = (iconName) => {
    const iconMap = {
      ClipboardList: Clock,
      Hammer: AlertCircle,
      CheckCircle: CheckCircle,
      DollarSign: DollarSign,
      Key: Key,
      Wrench: Wrench,
      Package: Package,
      FileText: FileText,
    };
    return iconMap[iconName] || Package;
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Property Status Management</h1>
          <p className="text-gray-600 mt-1">Manage property statuses for your real estate projects</p>
        </div>
        <Button onClick={() => openModal('create')} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Property Status
        </Button>
      </div>

      {/* Filters */}
      <PropertyStatusSearch
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        setSearchTerm={setSearchTerm}
        setStatusFilter={setStatusFilter}
      />

      {/* Property Statuses Table */}
      <PropertyStatusTable
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        getIconComponent={getIconComponent}
        openModal={openModal}
        handleToggleStatus={handleToggleStatus}
        handleDelete={handleDelete}
      />

      {/* Create/Edit Modal */}
      <PropertyStatusModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        handleSubmit={handleSubmit}
        handleInputChange={handleInputChange}
        closeModal={closeModal}
        propertyStatus={selectedStatus}
        modalMode={modalMode}
        formData={formData}
        colorOptions={colorOptions}
        iconOptions={iconOptions}
        loading={loading}
        getIconComponent={getIconComponent}
      />
    </div>
  );
};

export default PropertyStatusManagement;
