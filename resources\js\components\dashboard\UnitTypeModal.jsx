import React, { useState, useEffect } from "react";
import unitTypeAPI from '../../services/unitTypeAPI';

export default function UnitTypeModal({ isOpen, onClose, onSuccess, unitType, mode }) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: '',
    color: '',
    is_active: true,
    sort_order: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isOpen) {
      if (unitType && (mode === 'edit' || mode === 'view')) {
        setFormData({
          name: unitType.name || '',
          description: unitType.description || '',
          icon: unitType.icon || '',
          color: unitType.color || '',
          is_active: unitType.is_active ?? true,
          sort_order: unitType.sort_order ?? 0,
        });
      } else {
        setFormData({
          name: '',
          description: '',
          icon: '',
          color: '',
          is_active: true,
          sort_order: 0,
        });
      }
      setError(null);
    }
  }, [isOpen, unitType, mode]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isReadOnly = mode === 'view';
  if (!isOpen) return null;

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);
    try {
      if (mode === 'create') {
        await unitTypeAPI.create(formData);
      } else if (mode === 'edit' && unitType) {
        await unitTypeAPI.update(unitType.id, formData);
      }
      onSuccess();
    } catch (err) {
      setError(err.message || 'Failed to save unit type');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div>
            <h3 className="text-lg font-semibold">
              {mode === 'create'
                ? 'Add New Unit Type'
                : mode === 'edit'
                ? 'Edit Unit Type'
                : 'View Unit Type'}
            </h3>
            <p className="text-blue-100 text-sm">
              {mode === 'create'
                ? 'Create a new unit type category'
                : mode === 'edit'
                ? 'Update unit type information'
                : 'View unit type details'}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {error && <p className="text-red-600 mb-4">{error}</p>}
          <form className="space-y-4" onSubmit={e => { e.preventDefault(); handleSubmit(); }}>
            <div className="space-y-2">
              <label htmlFor="name" className="block font-medium">
                Unit Type Name *
              </label>
              <input
                id="name"
                type="text"
                value={formData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                placeholder="e.g., Studio, 1 Bedroom, Villa"
                disabled={isReadOnly}
                className="border rounded px-2 py-1 w-full"
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="description" className="block font-medium">
                Description
              </label>
              <textarea
                id="description"
                value={formData.description}
                onChange={e => handleInputChange('description', e.target.value)}
                placeholder="Brief description of this unit type"
                rows={3}
                disabled={isReadOnly}
                className="border rounded px-2 py-1 w-full"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="icon" className="block font-medium">
                Icon
              </label>
              <input
                id="icon"
                type="text"
                value={formData.icon}
                onChange={e => handleInputChange('icon', e.target.value)}
                placeholder="e.g., Home, Building, Star"
                disabled={isReadOnly}
                className="border rounded px-2 py-1 w-full"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="color" className="block font-medium">
                Color
              </label>
              <input
                id="color"
                type="text"
                value={formData.color}
                onChange={e => handleInputChange('color', e.target.value)}
                placeholder="e.g., #4facfe or bg-blue-100"
                disabled={isReadOnly}
                className="border rounded px-2 py-1 w-full"
              />
              <p className="text-xs text-gray-500">Enter color code or Tailwind class</p>
            </div>

            <div className="space-y-2">
              <label htmlFor="sort_order" className="block font-medium">
                Sort Order
              </label>
              <input
                id="sort_order"
                type="number"
                min="0"
                value={formData.sort_order}
                onChange={e => handleInputChange('sort_order', parseInt(e.target.value) || 0)}
                placeholder="0"
                disabled={isReadOnly}
                className="border rounded px-2 py-1 w-full"
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                id="is_active"
                type="checkbox"
                checked={formData.is_active}
                onChange={e => handleInputChange('is_active', e.target.checked)}
                disabled={isReadOnly}
                className="rounded"
              />
              <label htmlFor="is_active" className="font-medium">
                Active Status
              </label>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-300 rounded"
                disabled={loading}
              >
                {mode === 'view' ? 'Close' : 'Cancel'}
              </button>
              {mode !== 'view' && (
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded"
                  disabled={loading}
                >
                  {loading
                    ? mode === 'create'
                      ? 'Creating...'
                      : 'Updating...'
                    : mode === 'create'
                    ? 'Create Unit Type'
                    : 'Update Unit Type'}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
