import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge, Edit2, Power, Trash2, MoreHorizontal } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const PaymentStatusTable = ({
    status,
    handleEdit,
    handleToggleStatus,
    handleDelete,
}) => {
    return (
        <Card key={status.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg text-gray-900 mb-2">
                            {status.name}
                        </h3>
                        {status.description && (
                            <p className="text-gray-600 text-sm line-clamp-2">
                                {status.description}
                            </p>
                        )}
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleEdit(status)}>
                                <Edit2 className="mr-2 h-4 w-4" />
                                Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleToggleStatus(status)}>
                                <Power className="mr-2 h-4 w-4" />
                                {status.is_active ? 'Deactivate' : 'Activate'}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={() => handleDelete(status)}
                                className="text-red-600"
                            >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                <div className="flex justify-between items-center">
                    <Badge
                        variant="secondary"
                        className={status.is_active ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'}
                    >
                        {status.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                    <div className="text-sm text-gray-500">
                        Order: {status.sort_order}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default PaymentStatusTable;
