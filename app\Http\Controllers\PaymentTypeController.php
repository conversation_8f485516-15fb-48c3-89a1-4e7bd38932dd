<?php

namespace App\Http\Controllers;
use App\Models\PaymentType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PaymentTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try {
            $query = PaymentType::query();

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where('name', 'LIKE', "%{$search}%");
            }

            // Status filter
            if ($request->has('status') && $request->status) {
                $query->where('is_active', $request->status === 'active');
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortOrder = $request->get('sort_order', 'asc');
            if (in_array($sortBy, ['name', 'description', 'sort_order', 'created_at'])) {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $paymentTypes = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $paymentTypes
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment types: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:payment_types,name',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $paymentType = PaymentType::create($validated);
            return response()->json([
                'success' => true,
                'data' => $paymentType,
                'message' => 'Payment type created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse

    {
        //
        try {
            $paymentType = PaymentType::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $paymentType,
                'message' => 'Payment type retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        //
        try {
            $paymentType = PaymentType::findOrFail($id);
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:payment_types,name,' . $id,
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $paymentType->update($validated);
            return response()->json([
                'success' => true,
                'data' => $paymentType->fresh(),
                'message' => 'Payment type updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        //
        try {
            $paymentType = PaymentType::findOrFail($id);
            $paymentType->delete();
            return response()->json([
                'success' => true,
                'message' => 'Payment type deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete payment type: ' . $e->getMessage()
            ], 500);
        }
    }

    public function toggleStatus(string $id): JsonResponse
    {
        try{    
            $paymentType = PaymentType::findOrFail($id);
            $paymentType->is_active = !$paymentType->is_active;
            $paymentType->save();
            return response()->json([
                'success' => true,
                'data' => $paymentType,
                'message' => 'Payment type status updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment type status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function dropdown(): JsonResponse
    {
        try{    
            $paymentTypes = PaymentType::active()
                ->ordered()
                ->select('id', 'name', 'description')
                ->get();
            return response()->json([
                'success' => true,
                'data' => $paymentTypes,
                'message' => 'Payment types for dropdown retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment types for dropdown',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
