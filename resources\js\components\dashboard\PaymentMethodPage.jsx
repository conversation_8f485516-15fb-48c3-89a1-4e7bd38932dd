import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Search,
  Plus,
  Edit2,
  Trash2,
  Power,
  MoreHorizontal,
  Settings,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import paymentMethodAPI from '@/services/paymentMethodAPI';
import { toast } from 'react-hot-toast';

// Modal component
function PaymentMethodModal({ isOpen, onClose, method, mode, onSave }) {
  const [formData, setFormData] = useState(
    method || { name: '', description: '', is_active: true }
  );
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    setFormData(method || { name: '', description: '', is_active: true });
  }, [method]);

  if (!isOpen) return null;

  const handleSave = async () => {
    if (!formData.name.trim()) {
      toast.error('Name is required');
      return;
    }
    setSaving(true);
    try {
      if (mode === 'edit') {
        await paymentMethodAPI.update(method.id, formData);
        toast.success('Payment method updated successfully');
      } else {
        await paymentMethodAPI.create(formData);
        toast.success('Payment method created successfully');
      }
      onSave();
      onClose();
    } catch (err) {
      toast.error(err.message || 'Failed to save payment method');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-md flex flex-col overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <Settings className="h-6 w-6" />
            <div>
              <h3 className="text-lg font-semibold">
                {mode === 'create'
                  ? 'Add Payment Method'
                  : mode === 'edit'
                  ? 'Edit Payment Method'
                  : 'View Payment Method'}
              </h3>
              <p className="text-blue-100 text-sm">
                {mode === 'create'
                  ? 'Create a new payment method'
                  : mode === 'edit'
                  ? 'Update payment method info'
                  : 'View payment method details'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="flex-1 flex flex-col overflow-y-auto px-6 py-4 min-h-0">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label className="text-right text-sm font-medium col-span-1">Name</label>
              <Input
                value={formData.name}
                disabled={mode === 'view'}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label className="text-right text-sm font-medium col-span-1">Description</label>
              <textarea
                className="col-span-3 w-full border rounded p-2"
                rows={3}
                value={formData.description}
                disabled={mode === 'view'}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>
            {mode !== 'view' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <label className="text-right text-sm font-medium col-span-1 flex items-center">Active</label>
                <div className="col-span-3 flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  />
                  <span>Active</span>
                </div>
              </div>
            )}
          </div>
          {mode !== 'view' && (
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={onClose} disabled={saving}>Cancel</Button>
              <Button onClick={handleSave} disabled={saving}>
                {saving ? 'Saving...' : mode === 'edit' ? 'Update' : 'Create'}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function PaymentMethodPage() {
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedMethod, setSelectedMethod] = useState(null);

  // Fetch payment methods from API
  const fetchMethods = async () => {
    setLoading(true);
    try {
      const res = await paymentMethodAPI.getAll();
      let methods = [];
      if (res && Array.isArray(res.data)) {
        methods = res.data;
      } else if (res && res.data && Array.isArray(res.data.data)) {
        methods = res.data.data;
      }
      setPaymentMethods(methods);
    } catch (err) {
      setError(err.message || 'Failed to fetch payment methods');
      toast.error(err.message || 'Failed to fetch payment methods');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMethods();
  }, []);

  const handleCreate = () => {
    setSelectedMethod(null);
    setModalMode('create');
    setIsModalOpen(true);
  };
  const handleEdit = (method) => {
    setSelectedMethod(method);
    setModalMode('edit');
    setIsModalOpen(true);
  };
  const handleView = (method) => {
    setSelectedMethod(method);
    setModalMode('view');
    setIsModalOpen(true);
  };
  const handleDelete = async (method) => {
    if (window.confirm(`Delete payment method "${method.name}"?`)) {
      try {
        await paymentMethodAPI.delete(method.id);
        toast.success('Payment method deleted');
        fetchMethods();
      } catch (err) {
        toast.error(err.message || 'Failed to delete payment method');
      }
    }
  };
  const handleToggleStatus = async (method) => {
    try {
      await paymentMethodAPI.toggleStatus(method.id);
      toast.success(`Payment method ${method.is_active ? 'deactivated' : 'activated'}`);
      fetchMethods();
    } catch (err) {
      toast.error(err.message || 'Failed to toggle status');
    }
  };

  const filteredMethods = paymentMethods.filter((m) =>
    m.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (statusFilter === 'all' || (statusFilter === 'active' && m.is_active) || (statusFilter === 'inactive' && !m.is_active))
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Payment Methods</h1>
          <p className="text-muted-foreground">Manage your payment method options</p>
        </div>
        <Button className="gap-2" onClick={handleCreate}>
          <Plus className="h-4 w-4" /> Add Payment Method
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search payment methods..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Payment Methods List */}
      {loading ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading payment methods...</p>
          </CardContent>
        </Card>
      ) : filteredMethods.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMethods.map((method) => (
            <Card key={method.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="font-semibold text-lg text-gray-900">{method.name}</h3>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleView(method)}>
                        <Settings className="mr-2 h-4 w-4" /> View
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(method)}>
                        <Edit2 className="mr-2 h-4 w-4" /> Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleToggleStatus(method)}>
                        <Power className="mr-2 h-4 w-4" /> {method.is_active ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(method)}>
                        <Trash2 className="mr-2 h-4 w-4" /> Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <p className="text-gray-600 text-sm mb-4">{method.description || 'No description provided.'}</p>
                <Badge className={method.is_active ? 'bg-green-500' : 'bg-gray-500'}>
                  {method.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No payment methods found</h3>
            <p className="text-gray-500 mb-4">Create your first payment method to get started.</p>
            <Button className="gap-2" onClick={handleCreate}>
              <Plus className="h-4 w-4" /> Add Payment Method
            </Button> 
          </CardContent>
        </Card>
      )}

      {/* Modal */}
      <PaymentMethodModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        method={selectedMethod}
        mode={modalMode}
        onSave={fetchMethods}
      />
    </div>
  );
}
