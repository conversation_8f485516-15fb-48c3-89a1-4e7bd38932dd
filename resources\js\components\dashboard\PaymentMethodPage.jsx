import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import PaymentMethodSearch from './PaymentMethodSearch';

import PaymentMethodTable from './PaymentMethodTable';
import {
  Search,
  Plus,
  Edit2,
  Trash2,
  Power,
  MoreHorizontal,
  Settings,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import paymentMethodAPI from '@/services/paymentMethodAPI';
import { toast } from 'react-hot-toast';

// Modal component
function PaymentMethodModal({ isOpen, onClose, method, mode, onSave }) {
  const [formData, setFormData] = useState(
    method || { name: '', description: '', is_active: true }
  );
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    setFormData(method || { name: '', description: '', is_active: true });
  }, [method]);

  if (!isOpen) return null;

  const handleSave = async () => {
    if (!formData.name.trim()) {
      toast.error('Name is required');
      return;
    }
    setSaving(true);
    try {
      if (mode === 'edit') {
        await paymentMethodAPI.update(method.id, formData);
        toast.success('Payment method updated successfully');
      } else {
        await paymentMethodAPI.create(formData);
        toast.success('Payment method created successfully');
      }
      onSave();
      onClose();
    } catch (err) {
      toast.error(err.message || 'Failed to save payment method');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-md flex flex-col overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <Settings className="h-6 w-6" />
            <div>
              <h3 className="text-lg font-semibold">
                {mode === 'create'
                  ? 'Add Payment Method'
                  : mode === 'edit'
                  ? 'Edit Payment Method'
                  : 'View Payment Method'}
              </h3>
              <p className="text-blue-100 text-sm">
                {mode === 'create'
                  ? 'Create a new payment method'
                  : mode === 'edit'
                  ? 'Update payment method info'
                  : 'View payment method details'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Modal Content */}
        <div className="flex-1 flex flex-col overflow-y-auto px-6 py-4 min-h-0">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
                placeholder="Payment method name"
                disabled={mode === 'view'}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="col-span-3"
                placeholder="Payment method description"
                rows={3}
                disabled={mode === 'view'}
              />
            </div>
            {mode !== 'view' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="is_active" className="text-right">
                  Active
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Checkbox
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                  />
                  <Label htmlFor="is_active" className="text-sm font-normal">
                    Payment method is active
                  </Label>
                </div>
              </div>
            )}
          </div>
          {mode !== 'view' && (
            <div className="flex justify-end gap-2 mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleSave}
                disabled={saving || !formData.name.trim()}
              >
                {saving ? 'Saving...' : (mode === 'edit' ? 'Update' : 'Create')}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function PaymentMethodPage() {
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedMethod, setSelectedMethod] = useState(null);

  // Fetch payment methods from API
  const fetchMethods = async () => {
    setLoading(true);
    try {
      const res = await paymentMethodAPI.getAll();
      let methods = [];
      if (res && Array.isArray(res.data)) {
        methods = res.data;
      } else if (res && res.data && Array.isArray(res.data.data)) {
        methods = res.data.data;
      }
      setPaymentMethods(methods);
    } catch (err) {
      setError(err.message || 'Failed to fetch payment methods');
      toast.error(err.message || 'Failed to fetch payment methods');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMethods();
  }, []);

  const handleCreate = () => {
    setSelectedMethod(null);
    setModalMode('create');
    setIsModalOpen(true);
  };
  const handleEdit = (method) => {
    setSelectedMethod(method);
    setModalMode('edit');
    setIsModalOpen(true);
  };
  const handleView = (method) => {
    setSelectedMethod(method);
    setModalMode('view');
    setIsModalOpen(true);
  };
  const handleDelete = async (method) => {
    if (window.confirm(`Delete payment method "${method.name}"?`)) {
      try {
        await paymentMethodAPI.delete(method.id);
        toast.success('Payment method deleted');
        fetchMethods();
      } catch (err) {
        toast.error(err.message || 'Failed to delete payment method');
      }
    }
  };
  const handleToggleStatus = async (method) => {
    try {
      await paymentMethodAPI.toggleStatus(method.id);
      toast.success(`Payment method ${method.is_active ? 'deactivated' : 'activated'}`);
      fetchMethods();
    } catch (err) {
      toast.error(err.message || 'Failed to toggle status');
    }
  };

  const filteredMethods = paymentMethods.filter((m) =>
    m.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (statusFilter === 'all' || (statusFilter === 'active' && m.is_active) || (statusFilter === 'inactive' && !m.is_active))
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Payment Methods</h1>
          <p className="text-muted-foreground">Manage your payment method options</p>
        </div>
        <Button className="gap-2" onClick={handleCreate}>
          <Plus className="h-4 w-4" /> Add Payment Method
        </Button>
      </div>

      {/* Filters */}
     <PaymentMethodSearch
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        setSearchTerm={setSearchTerm}
        setStatusFilter={setStatusFilter}
      />

      {/* Payment Methods List */}
      {loading ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading payment methods...</p>
          </CardContent>
        </Card>
      ) : filteredMethods.length > 0 ? (
        <PaymentMethodTable
          filteredMethods={filteredMethods}
          handleView={handleView}
          handleEdit={handleEdit}
          handleToggleStatus={handleToggleStatus}
          handleDelete={handleDelete}
        />
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No payment methods found</h3>
            <p className="text-gray-500 mb-4">Create your first payment method to get started.</p>
            <Button className="gap-2" onClick={handleCreate}>
              <Plus className="h-4 w-4" /> Add Payment Method
            </Button> 
          </CardContent>
        </Card>
      )}

      {/* Modal */}
      <PaymentMethodModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        method={selectedMethod}
        mode={modalMode}
        onSave={fetchMethods}
      />
    </div>
  );
}
