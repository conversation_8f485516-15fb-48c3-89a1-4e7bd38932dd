import axios from "axios";
const API_URL = "/api/property-services";

// Create axios instance with default config
const api = axios.create({
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
    },
})

// Add request interceptor to include auth token
api.interceptors.request.use(
    (config)=>{
        const token = localStorage.getItem('auth_token')
        if(token){
            config.headers.Authorization = `Bearer ${token}`
        }
        return config;
    },
    (error)=>{
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response)=>response,
    (error)=>{
        if(error.response?.status === 401){
            localStorage.removeItem('auth_token');
            window.location.href='/login';
        }
        return Promise.reject(error);
    }
);

const propertyServiceAPI = {
    // Get all property services with pagination and filters
    getall: async (params ={})=>{
        try{
            const response = await api.get(API_URL, {params});
            // Handle paginated response - extract the data array from pagination
            if (response.data.success && response.data.data && response.data.data.data) {
                return response.data.data.data; // Paginated data
            }
            return response.data.data || response.data;
        }catch(error){
            console.error('Error fetching property services:', error);
            throw error.response?.data || error;
        }
    },

    // Get property service by ID
    getByid: async (id)=>{
        try{
            const response =await api.get(API_URL + `/${id}`);
            return response.data.data || response.data;
        }catch(error){
            console.error('Error fetching property service:', error);
            throw error.response?.data || error;
        }
    },

    // Create new property service
    create: async (data)=>{
        try{
            const response=await api.post(API_URL,data);
            return response.data.data || response.data;
        }catch(error){
            console.error('Error creating property service:', error);
            throw error.response?.data || error;
        }
    },

    // Update property service
    update: async (id,data)=>{
        try{
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data.data || response.data;

        }catch(error){
            console.error('Error updating property service:', error);
            throw error.response?.data || error;
        }
    },

    // Delete property service
    delete: async (id)=>{
        try{
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data.data || response.data;
        }catch(error){
            console.error('Error deleting property service:', error);
            throw error.response?.data || error;
        }
    },

    // Toggle property service status (active/inactive)
    toggleStatus: async (id)=>{
        try{
            const response =await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data.data || response.data;
        }catch(error){
            console.error('Error toggling property service status:', error);
            throw error.response?.data || error;
        }
    },

    // Get dropdown options (active property services only)
    getDropdown: async() => {
        try {
            const response = await api.get(`${API_URL}/dropdown`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching property services dropdown:', error);
            throw error.response?.data || error;
        }
    }
}

export default propertyServiceAPI;
