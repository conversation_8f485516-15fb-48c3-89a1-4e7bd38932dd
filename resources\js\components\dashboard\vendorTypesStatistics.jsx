    import React from "react";
    import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
    import {Package,Check,X} from "lucide-react";
  
    const VendorTypesStatistics = ({ statistics }) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-800">Total Vendor Types</CardTitle>
                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <Package className="h-4 w-4 text-white" />
                </div>
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold text-blue-900">{statistics.total_vendor_types || 0}</div>
                <p className="text-xs text-blue-600 mt-1">All vendor type categories</p>
            </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-green-800">Active Types</CardTitle>
                <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                <Check className="h-4 w-4 text-white" />
                </div>
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold text-green-900">{statistics.active_vendor_types || 0}</div>
                <p className="text-xs text-green-600 mt-1">Currently available</p>
            </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-red-800">Inactive Types</CardTitle>
                <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                <X className="h-4 w-4 text-white" />
                </div>
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold text-red-900">{statistics.inactive_vendor_types || 0}</div>
                <p className="text-xs text-red-600 mt-1">Not currently active</p>
            </CardContent>
            </Card>
        </div>

    )};

    export default VendorTypesStatistics;
