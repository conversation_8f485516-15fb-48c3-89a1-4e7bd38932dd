import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import RentTypesSearch from './RentTypesSearch';
import RentTypesTable from './RentTypesTable';
import RentTypeModal from './RentTypeModal';  
import {
  Plus,
  Settings,
} from 'lucide-react';  

import rentTypeAPI from '@/services/rentTypeAPI';

export default function RentTypePage() {
  const [rentTypes, setRentTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [editRentType, setEditRentType] = useState(null);
  const [form, setForm] = useState({ name: '', description: '', is_active: true, sort_order: 0 });
  const [saving, setSaving] = useState(false);

  // Fetch all rent types using rentTypeAPI
  const fetchRentTypes = async () => {
    setLoading(true);
    try {
      const data = await rentTypeAPI.getAll();
      // Handle paginated response
      if (data.success && data.data && data.data.data) {
        setRentTypes(data.data.data);
      } else {
        setRentTypes(data.data || data);
      }
    } catch (err) {
      setError(err.message || 'Failed to fetch rent types');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRentTypes();
  }, []);

  // Create or update rent type
  const handleSave = async (formData) => {
    setSaving(true);
    try {
      if (editRentType) {
        await rentTypeAPI.update(editRentType.id, formData);
      } else {
        await rentTypeAPI.create(formData);
      }
      setModalOpen(false);
      setForm({ name: '', description: '', is_active: true, sort_order: 0 });
      setEditRentType(null);
      fetchRentTypes();
    } catch (err) {
      setError(err.message || 'Failed to save rent type');
    } finally {
      setSaving(false);
    }
  };

  // Edit handler
  const handleEdit = (rentType) => {
    setEditRentType(rentType);
    setForm({
      name: rentType.name,
      description: rentType.description,
      is_active: rentType.is_active,
      sort_order: rentType.sort_order || 0,
    });
    setModalOpen(true);
  };

  // Delete handler
  const handleDelete = async (rentType) => {
    if (window.confirm(`Delete rent type "${rentType.name}"?`)) {
      setSaving(true);
      try {
        await rentTypeAPI.delete(rentType.id);
        fetchRentTypes();
      } catch (err) {
        setError(err.message || 'Failed to delete rent type');
      } finally {
        setSaving(false);
      }
    }
  };

  // Toggle status handler
  const handleToggleStatus = async (rentType) => {
    setSaving(true);
    try {
      await rentTypeAPI.toggleStatus(rentType.id);
      fetchRentTypes();
    } catch (err) {
      setError(err.message || 'Failed to toggle status');
    } finally {
      setSaving(false);
    }
  };

  // Open modal for create
  const handleCreate = () => {
    setEditRentType(null);
    setForm({ name: '', description: '', is_active: true, sort_order: 0 });
    setModalOpen(true);
  };

  // Filtered rent types for search and status
  const filteredRentTypes = rentTypes.filter((rentType) => {
    const matchesSearch = rentType.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && rentType.is_active) ||
      (statusFilter === 'inactive' && !rentType.is_active);
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Rent Types</h1>
          <p className="text-muted-foreground">
            Manage available rent type categories
          </p>
        </div>
        <Button onClick={handleCreate} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Rent Type
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <span className="text-sm font-medium">Error:</span>
              <span className="text-sm">{error}</span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setError(null)}
                className="ml-auto"
              >
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <RentTypesSearch
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        setSearchTerm={setSearchTerm}
        setStatusFilter={setStatusFilter}
      />

      {/* Loading State */}
      {loading ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading rent types...</p>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Rent Types Grid */}
          {filteredRentTypes.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRentTypes.map((rentType) => (
                <RentTypesTable
                  key={rentType.id}
                  rentType={rentType}
                  handleEdit={handleEdit}
                  handleToggleStatus={handleToggleStatus}
                  handleDelete={handleDelete}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No rent types found</h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm || statusFilter !== 'all'
                    ? 'No rent types match your current filters.'
                    : 'Get started by creating your first rent type.'}
                </p>
                {(!searchTerm && statusFilter === 'all') && (
                  <Button onClick={handleCreate} className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Rent Type
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Create/Edit Modal */}
      <RentTypeModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        onSuccess={handleSave}
        rentType={editRentType}
        mode={editRentType ? 'edit' : 'create'}
      />
    </div>
  );
}
