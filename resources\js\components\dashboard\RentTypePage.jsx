import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';

import {
  Search,
  Plus,
  Edit2,
  Trash2,
  Power,
  MoreHorizontal,
  Settings,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import rentTypeAPI from '@/services/rentTypeAPI';

export default function RentTypePage() {
  const [rentTypes, setRentTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [editRentType, setEditRentType] = useState(null);
  const [form, setForm] = useState({ name: '', description: '', is_active: true, sort_order: 0 });
  const [saving, setSaving] = useState(false);

  // Fetch all rent types using rentTypeAPI
  const fetchRentTypes = async () => {
    setLoading(true);
    try {
      const data = await rentTypeAPI.getAll();
      // Handle paginated response
      if (data.success && data.data && data.data.data) {
        setRentTypes(data.data.data);
      } else {
        setRentTypes(data.data || data);
      }
    } catch (err) {
      setError(err.message || 'Failed to fetch rent types');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRentTypes();
  }, []);

  // Create or update rent type
  const handleSave = async () => {
    setSaving(true);
    try {
      if (editRentType) {
        await rentTypeAPI.update(editRentType.id, form);
      } else {
        await rentTypeAPI.create(form);
      }
      setModalOpen(false);
      setForm({ name: '', description: '', is_active: true, sort_order: 0 });
      setEditRentType(null);
      fetchRentTypes();
    } catch (err) {
      setError(err.message || 'Failed to save rent type');
    } finally {
      setSaving(false);
    }
  };

  // Edit handler
  const handleEdit = (rentType) => {
    setEditRentType(rentType);
    setForm({
      name: rentType.name,
      description: rentType.description,
      is_active: rentType.is_active,
      sort_order: rentType.sort_order || 0,
    });
    setModalOpen(true);
  };

  // Delete handler
  const handleDelete = async (rentType) => {
    if (window.confirm(`Delete rent type "${rentType.name}"?`)) {
      setSaving(true);
      try {
        await rentTypeAPI.delete(rentType.id);
        fetchRentTypes();
      } catch (err) {
        setError(err.message || 'Failed to delete rent type');
      } finally {
        setSaving(false);
      }
    }
  };

  // Toggle status handler
  const handleToggleStatus = async (rentType) => {
    setSaving(true);
    try {
      await rentTypeAPI.toggleStatus(rentType.id);
      fetchRentTypes();
    } catch (err) {
      setError(err.message || 'Failed to toggle status');
    } finally {
      setSaving(false);
    }
  };

  // Open modal for create
  const handleCreate = () => {
    setEditRentType(null);
    setForm({ name: '', description: '', is_active: true, sort_order: 0 });
    setModalOpen(true);
  };

  // Filtered rent types for search and status
  const filteredRentTypes = rentTypes.filter((rentType) => {
    const matchesSearch = rentType.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && rentType.is_active) ||
      (statusFilter === 'inactive' && !rentType.is_active);
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Rent Types</h1>
          <p className="text-muted-foreground">
            Manage available rent type categories
          </p>
        </div>
        <Button onClick={handleCreate} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Rent Type
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <span className="text-sm font-medium">Error:</span>
              <span className="text-sm">{error}</span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setError(null)}
                className="ml-auto"
              >
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search rent types..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading rent types...</p>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Rent Types Grid */}
          {filteredRentTypes.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRentTypes.map((rentType) => (
                <Card key={rentType.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg text-gray-900 mb-2">
                          {rentType.name}
                        </h3>
                        {rentType.description && (
                          <p className="text-gray-600 text-sm line-clamp-2">
                            {rentType.description}
                          </p>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleEdit(rentType)}>
                            <Edit2 className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleToggleStatus(rentType)}>
                            <Power className="mr-2 h-4 w-4" />
                            {rentType.is_active ? 'Deactivate' : 'Activate'}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDelete(rentType)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <div className="flex justify-between items-center">
                      <Badge
                        variant={rentType.is_active ? 'default' : 'secondary'}
                        className={rentType.is_active ? 'bg-green-500' : 'bg-gray-500'}
                      >
                        {rentType.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                      <div className="text-sm text-gray-500">
                        Order: {rentType.sort_order}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No rent types found</h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm || statusFilter !== 'all'
                    ? 'No rent types match your current filters.'
                    : 'Get started by creating your first rent type.'}
                </p>
                {(!searchTerm && statusFilter === 'all') && (
                  <Button onClick={handleCreate} className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Rent Type
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Create/Edit Modal */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-2xl w-full max-w-md max-h-[90vh] flex flex-col overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
              <div className="flex items-center space-x-3">
                <Settings className="h-6 w-6" />
                <div>
                  <h3 className="text-lg font-semibold">
                    {editRentType ? 'Edit Rent Type' : 'Create New Rent Type'}
                  </h3>
                  <p className="text-blue-100 text-sm">
                    {editRentType
                      ? 'Update the rent type details below.'
                      : 'Fill in the details to create a new rent type.'}
                  </p>
                </div>
              </div>
              <button
                onClick={() => setModalOpen(false)}
                className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            {/* Modal Content */}
            <div className="flex-1 flex flex-col overflow-y-auto px-6 py-4 min-h-0">
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={form.name}
                    onChange={(e) => setForm({ ...form, name: e.target.value })}
                    className="col-span-3"
                    placeholder="Rent type name"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    value={form.description}
                    onChange={(e) => setForm({ ...form, description: e.target.value })}
                    className="col-span-3"
                    placeholder="Rent type description"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="sort_order" className="text-right">
                    Sort Order
                  </Label>
                  <Input
                    id="sort_order"
                    type="number"
                    value={form.sort_order}
                    onChange={(e) => setForm({ ...form, sort_order: parseInt(e.target.value) || 0 })}
                    className="col-span-3"
                    placeholder="0"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="is_active" className="text-right">
                    Active
                  </Label>
                  <div className="col-span-3 flex items-center space-x-2">
                    <Checkbox
                      id="is_active"
                      checked={form.is_active}
                      onCheckedChange={(checked) => setForm({ ...form, is_active: checked })}
                    />
                    <Label htmlFor="is_active" className="text-sm font-normal">
                      Rent type is active
                    </Label>
                  </div>
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setModalOpen(false)}
                  disabled={saving}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleSave}
                  disabled={saving || !form.name.trim()}
                >
                  {saving ? 'Saving...' : (editRentType ? 'Update' : 'Create')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
