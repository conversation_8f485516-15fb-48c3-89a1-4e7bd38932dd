import axios from 'axios';

const API_URL = '/api/property-stages';
// import axios instance with default config
const api = axios.create({ 
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});   


// Add request interceptor to include auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // Handle unauthorized access
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

const propertyStageAPI = {
    // Get all property stages with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching property stages:', error);
            throw error.response?.data || error;
        }
    },

    // Get property stage by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching property stage:', error);
            throw error.response?.data || error;
        }
    },

    // Create new property stage
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data;
        } catch (error) {
            console.error('Error creating property stage:', error);
            throw error.response?.data || error;
        }
    },

    // Update property stage
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data;
        } catch (error) {
            console.error('Error updating property stage:', error);
            throw error.response?.data || error;
        }
    },

    // Delete property stage
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting property stage:', error);
            throw error.response?.data || error;
        }
    },

    // Toggle property stage status (active/inactive)
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data;
        } catch (error) {
            console.error('Error toggling property stage status:', error);
            throw error.response?.data || error;
        }
    },

    // Get dropdown options (active property stages only)
    getDropdown: async () => {
        try {
            const response = await api.get(`${API_URL}/dropdown`);
            return response.data;
        } catch (error) {
            console.error('Error fetching property stages dropdown:', error);
            throw error.response?.data || error;
        }
    }
}

export default propertyStageAPI;
