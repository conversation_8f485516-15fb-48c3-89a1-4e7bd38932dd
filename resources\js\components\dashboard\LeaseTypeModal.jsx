import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { X, Settings } from "lucide-react";
import leaseTypeAPI from '@/services/leaseTypeAPI';

const LeaseTypeModal = ({
  isOpen,
  onClose,
  onSuccess,
  leaseType,
  mode
}) => {
  const [form, setForm] = useState({
    name: '',
    description: '',
    sort_order: 0,
    is_active: true
  });
  const [saving, setSaving] = useState(false);

  // Initialize form when modal opens or when leaseType changes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && leaseType) {
        setForm({
          name: leaseType.name || '',
          description: leaseType.description || '',
          sort_order: leaseType.sort_order || 0,
          is_active: leaseType.is_active ?? true
        });
      } else {
        setForm({
          name: '',
          description: '',
          sort_order: 0,
          is_active: true
        });
      }
    }
  }, [isOpen, mode, leaseType]);

  const handleSave = async () => {
    setSaving(true);
    try {
      console.log('Saving lease type with data:', form); // Debug log
      if (mode === 'create') {
        await leaseTypeAPI.create(form);
      } else {
        await leaseTypeAPI.update(leaseType.id, form);
      }
      onSuccess(); // This will refresh the data in parent component
      onClose();
    } catch (error) {
      console.error('Error saving lease type:', error);
      // Show validation errors to user
      if (error.response?.data?.errors) {
        const errors = Object.values(error.response.data.errors).flat().join('\n');
        alert(`Validation errors:\n${errors}`);
      } else {
        alert(error.response?.data?.message || error.message || 'Failed to save lease type');
      }
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-2xl w-full max-w-md max-h-[90vh] flex flex-col overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
              <div className="flex items-center space-x-3">
                <Settings className="h-6 w-6" />
                <div>
                  <h3 className="text-lg font-semibold">
                    {mode === 'edit' ? 'Edit Lease Type' : 'Create New Lease Type'}
                  </h3>
                  <p className="text-blue-100 text-sm">
                    {mode === 'edit'
                      ? 'Update the lease type details below.'
                      : 'Fill in the details to create a new lease type.'}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            {/* Modal Content */}
            <div className="flex-1 flex flex-col overflow-y-auto px-6 py-4 min-h-0">
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={form.name}
                    onChange={(e) => setForm({ ...form, name: e.target.value })}
                    className="col-span-3"
                    placeholder="Lease type name"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    value={form.description}
                    onChange={(e) => setForm({ ...form, description: e.target.value })}
                    className="col-span-3"
                    placeholder="Lease type description"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="sort_order" className="text-right">
                    Sort Order
                  </Label>
                  <Input
                    id="sort_order"
                    type="number"
                    value={form.sort_order}
                    onChange={(e) => setForm({ ...form, sort_order: parseInt(e.target.value) || 0 })}
                    className="col-span-3"
                    placeholder="0"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="is_active" className="text-right">
                    Active
                  </Label>
                  <div className="col-span-3 flex items-center space-x-2">
                    <Checkbox
                      id="is_active"
                      checked={form.is_active}
                      onCheckedChange={(checked) => setForm({ ...form, is_active: checked })}
                    />
                    <Label htmlFor="is_active" className="text-sm font-normal">
                      Lease type is active
                    </Label>
                  </div>
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={saving}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleSave}
                  disabled={saving || !form.name.trim()}
                >
                  {saving ? 'Saving...' : (mode === 'edit' ? 'Update' : 'Create')}
                </Button>
              </div>
            </div>
          </div>
        </div>
    )};
  export default LeaseTypeModal;
