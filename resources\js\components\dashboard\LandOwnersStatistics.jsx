import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, User, UserX, Calendar } from "lucide-react";
import { useTranslation } from '@/hooks/useTranslation';

const StatCard = ({ title, value, icon: Icon, color, description }) => {
    const colorClasses = {
        'bg-blue-500': 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-800',
        'bg-green-500': 'bg-gradient-to-r from-green-50 to-green-100 border-green-200 text-green-800',
        'bg-red-500': 'bg-gradient-to-r from-red-50 to-red-100 border-red-200 text-red-800',
        'bg-purple-500': 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200 text-purple-800',
    };
    
    const iconClasses = {
        'bg-blue-500': 'bg-blue-500',
        'bg-green-500': 'bg-green-500', 
        'bg-red-500': 'bg-red-500',
        'bg-purple-500': 'bg-purple-500',
    };
    
    const textClasses = {
        'bg-blue-500': 'text-blue-900',
        'bg-green-500': 'text-green-900',
        'bg-red-500': 'text-red-900', 
        'bg-purple-500': 'text-purple-900',
    };
    
    const descClasses = {
        'bg-blue-500': 'text-blue-600',
        'bg-green-500': 'text-green-600',
        'bg-red-500': 'text-red-600',
        'bg-purple-500': 'text-purple-600',
    };
    
    return (
        <Card className={colorClasses[color] || 'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200'}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className={`text-sm font-medium ${colorClasses[color]?.split(' ').pop() || 'text-gray-800'}`}>
                    {title}
                </CardTitle>
                <div className={`w-8 h-8 ${iconClasses[color] || 'bg-gray-500'} rounded-lg flex items-center justify-center`}>
                    <Icon className="h-4 w-4 text-white" />
                </div>
            </CardHeader>
            <CardContent>
                <div className={`text-2xl font-bold ${textClasses[color] || 'text-gray-900'}`}>
                    {value}
                </div>
                {description && (
                    <p className={`text-xs mt-1 ${descClasses[color] || 'text-gray-600'}`}>
                        {description}
                    </p>
                )}
            </CardContent>
        </Card>
    );
};

const LandOwnersStatistics = ({ statistics }) => {
    const { t } = useTranslation();
    
    return (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatCard
                title={t('landOwners.stats.totalLandOwners')}
                value={statistics.total_owners}
                icon={Users}
                color="bg-blue-500"
                description="Total registered land owners"
            />
            <StatCard
                title={t('landOwners.stats.activeLandOwners')}
                value={statistics.active_owners}
                icon={User}
                color="bg-green-500"
                description="Currently active owners"
            />
            <StatCard
                title={t('landOwners.stats.inactiveOwners')}
                value={statistics.inactive_owners}
                icon={UserX}
                color="bg-red-500"
                description="Inactive land owners"
            />
            <StatCard
                title={t('landOwners.stats.recentRegistrations')}
                value={statistics.recent_registrations}
                icon={Calendar}
                color="bg-purple-500"
                description="New owners in last 30 days"
            />
        </div>
    );
}

export default LandOwnersStatistics;
