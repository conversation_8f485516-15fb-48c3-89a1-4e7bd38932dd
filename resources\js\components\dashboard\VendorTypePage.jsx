import React, { useState, useEffect } from 'react';
import vendorTypeAPI from '../../services/vendorTypeAPI';
import Swal from 'sweetalert2';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import VendorTypesStatistics from './vendorTypesStatistics';
import VendorTypeSearch from './vendorTypeSearch';
import VendorTypeTable from './vendorTypeTable';
import VendorTypeModal from './VendorTypeModal';
import StandardModal from '@/components/ui/StandardModal';
import {  Plus, Edit2,Package } from 'lucide-react';

const VendorTypePage = () => {
  const [vendorTypes, setVendorTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({});
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [editingVendorType, setEditingVendorType] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    status: 'active'
  });

  // Pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');

  // Fetch vendor types
  const fetchVendorTypes = async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        per_page: perPage,
        search: searchTerm,
        status: statusFilter,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      const response = await vendorTypeAPI.getVendorTypes(params);
      setVendorTypes(response.data.data.data);
      setCurrentPage(response.data.data.current_page);
      setTotalPages(response.data.data.last_page);
      setTotalRecords(response.data.data.total);  
    } catch (error) {
      console.error('Error fetching vendor types:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to fetch vendor types',
        timer: 3000,
        showConfirmButton: false
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await vendorTypeAPI.getVendorTypeStatistics();
      setStatistics(response.data.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  useEffect(() => {
    fetchVendorTypes(1);
    fetchStatistics();
  }, [perPage, searchTerm, statusFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    fetchVendorTypes(1);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      status: 'active'
    });
  };

  // Handle add vendor type
  const handleAddVendorType = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Name is required',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }
    
    try {
      await vendorTypeAPI.createVendorType(formData);
      setShowAddModal(false);
      resetForm();
      fetchVendorTypes();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Vendor type created successfully',
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error creating vendor type:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error.response?.data?.message || 'Failed to create vendor type',
        timer: 3000,
        showConfirmButton: false
      });
    }
  };

  // Handle edit vendor type
  const handleEditVendorType = (vendorType) => {
    setEditingVendorType(vendorType);
    setFormData({
      name: vendorType.name || '',
      status: vendorType.status || 'active'
    });
    setShowEditModal(true);
  };

  // Handle update vendor type
  const handleUpdateVendorType = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Name is required',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }
    
    try {
      await vendorTypeAPI.updateVendorType(editingVendorType.id, formData);
      setShowEditModal(false);
      setEditingVendorType(null);
      resetForm();
      fetchVendorTypes();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Vendor type updated successfully',
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error updating vendor type:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error.response?.data?.message || 'Failed to update vendor type',
        timer: 3000,
        showConfirmButton: false
      });
    }
  };

  // Handle delete vendor type
  const handleDeleteVendorType = async (vendorType) => {
    const result = await Swal.fire({
      title: 'Delete Vendor Type',
      text: `Are you sure you want to delete "${vendorType.name}"? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      try {
        await vendorTypeAPI.deleteVendorType(vendorType.id);
        fetchVendorTypes();
        fetchStatistics();
        
        Swal.fire({
          icon: 'success',
          title: 'Deleted!',
          text: 'Vendor type deleted successfully',
          timer: 3000,
          showConfirmButton: false
        });
      } catch (error) {
        console.error('Error deleting vendor type:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to delete vendor type',
          timer: 3000,
          showConfirmButton: false
        });
      }
    }
  };

  // Handle status toggle
  const handleStatusToggle = async (vendorType) => {
    const newStatus = vendorType.status === 'active' ? 'inactive' : 'active';
    
    try {
      await vendorTypeAPI.updateVendorType(vendorType.id, {
        name: vendorType.name,
        status: newStatus
      });
      fetchVendorTypes();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: `Vendor type ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`,
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error toggling status:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to update vendor type status',
        timer: 3000,
        showConfirmButton: false
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Package className="w-8 h-8 mr-3 text-blue-600" />
            Vendor Type Management
          </h1>
          <p className="text-gray-600 mt-1">Manage vendor types and categories</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => setShowAddModal(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Vendor Type
        </Button>
      </div>

      {/* Statistics Cards */}
      <VendorTypesStatistics statistics={statistics} />

      {/* Filters */}
     <VendorTypeSearch
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        sortBy={sortBy}
        sortOrder={sortOrder}
        handleSearch={handleSearch}
        setSearchTerm={setSearchTerm}
        setStatusFilter={setStatusFilter}
        setSortBy={setSortBy}
        setSortOrder={setSortOrder}
      />

      {/* Vendor Types Table */}
    
      <VendorTypeTable 
        vendorTypes={vendorTypes} 
        loading={loading} 
        currentPage={currentPage} 
        totalPages={totalPages} 
        totalRecords={totalRecords} 
        fetchVendorTypes={fetchVendorTypes} 
        handleEditVendorType={handleEditVendorType} 
        handleDeleteVendorType={handleDeleteVendorType} 
        handleStatusToggle={handleStatusToggle} 
      />
      {/* Add Vendor Type Modal */}
        <VendorTypeModal 
          showAddModal={showAddModal} 
          setShowAddModal={setShowAddModal} 
          handleAddVendorType={handleAddVendorType} 
          formData={formData}
          setFormData={setFormData}
        />

      {/* Edit Vendor Type Modal */}
      <StandardModal 
        showModal={showEditModal} 
        closeModal={() => setShowEditModal(false)}
        modalMode="edit"
        title="Edit Vendor Type"
        icon={Edit2}
        maxWidth="max-w-md"
      >
        <div className="text-center pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
            <Package className="w-8 h-8 text-white" />
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Update vendor type information.
          </p>
        </div>

        <form onSubmit={handleUpdateVendorType} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name *</Label>
              <Input
                id="edit_name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="Enter vendor type name"
                required
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
              />
            </div>
            
            <div>
              <Label htmlFor="edit_status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                <SelectTrigger className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowEditModal(false)}
              className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <Edit2 className="mr-2 h-4 w-4 inline" />
              Update Vendor Type
            </button>
          </div>
        </form>
      </StandardModal>
    </div>
  );
};

export default VendorTypePage;
