import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuLabel, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Settings, Edit2, Power, Trash2 } from 'lucide-react';

const PaymentMethodTable = ({
    filteredMethods,
    handleView,
    handleEdit,
    handleToggleStatus,
    handleDelete,
}) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredMethods.map((method) => (
                <Card key={method.id} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                            <h3 className="font-semibold text-lg text-gray-900">{method.name}</h3>
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                        <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuItem onClick={() => handleView(method)}>
                                        <Settings className="mr-2 h-4 w-4" /> View
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleEdit(method)}>
                                        <Edit2 className="mr-2 h-4 w-4" /> Edit
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem onClick={() => handleToggleStatus(method)}>
                                        <Power className="mr-2 h-4 w-4" /> {method.is_active ? 'Deactivate' : 'Activate'}
                                    </DropdownMenuItem>
                                    <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(method)}>
                                        <Trash2 className="mr-2 h-4 w-4" /> Delete
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                        <p className="text-gray-600 text-sm mb-4">{method.description || 'No description provided.'}</p>
                        <Badge className={method.is_active ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'}>
                            {method.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
};
export default PaymentMethodTable;
